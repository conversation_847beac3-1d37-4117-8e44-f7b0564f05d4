import { notFound } from 'next/navigation';
import Layout from '@/components/layout/Layout';
import Markdown from '@/components/ui/Markdown';

// 页面数据接口
interface PageData {
  page_name: string;
  page_url: string;
  description: string;
  has_content: string;
  content_value: string;
}

// API响应接口
interface ApiResponse {
  status: string;
  data: PageData;
}

// 获取页面内容
async function getPageContent(slug: string): Promise<PageData | null> {
  try {
    const apiBaseUrl = 'https://api.planetclicker.pro';
    // 修改API请求URL格式，使用查询参数page_url
    const response = await fetch(`${apiBaseUrl}/pages/${slug}`, { 
      next: { revalidate: 120 } // 缓存2分钟
    });
    
    console.log(`请求URL: ${apiBaseUrl}/pages/${slug}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        console.error(`页面未找到: /${slug}`);
        return null;
      }
      throw new Error(`获取页面内容失败: ${response.status}`);
    }
    
    const data: ApiResponse = await response.json();
    console.log('API响应:', data);
    
    if (data.status === 'success' && data.data) {
      return data.data;
    }
    console.error('API响应中没有数据或状态不是success');
    return null;
  } catch (error) {
    console.error('获取页面内容时出错:', error);
    return null;
  }
}

export default async function CMSPage({ params }: { params: { slug: string } }) {
  // 确保在使用 params.slug 前先 await
  const resolvedParams = await Promise.resolve(params);
  const slug = resolvedParams.slug;
  
  // 使用解析后的 slug 获取页面内容
  const pageData = await getPageContent(slug);
  
  // 如果没有找到页面内容，显示404页面
  if (!pageData) {
    notFound();
  }
  
  // 转换页面标题为显示格式
  const pageTitle = pageData.page_name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
  
  return (
    <Layout>
      <div className="max-w-screen-xl mx-auto px-2 sm:px-4">
        <div className="bg-card rounded-lg p-3 sm:p-6 md:p-8 shadow-md">
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-4 sm:mb-6">{pageTitle}</h1>
          
          {pageData.description && (
            <div className="text-muted-foreground mb-4 sm:mb-8">
              <div className="prose prose-invert max-w-none prose-sm sm:prose-base">
                {/* 使用Markdown组件渲染description */}
                <Markdown content={pageData.description} />
              </div>
            </div>
          )}
          
          {pageData.has_content === "1" && pageData.content_value && (
            <div className="cms-content prose prose-invert prose-sm sm:prose-base max-w-none">
              {/* 使用条件渲染判断内容是HTML还是Markdown */}
              {pageData.content_value.includes('<') ? (
                <div dangerouslySetInnerHTML={{ __html: pageData.content_value }} />
              ) : (
                <Markdown content={pageData.content_value} />
              )}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
} 