"use client";

import type React from 'react';
import Link from 'next/link';
import { X } from 'lucide-react';

interface ControllerBannerProps {
  onClose?: () => void;
}

const ControllerBanner: React.FC<ControllerBannerProps> = ({ onClose }) => {
  return (
    <div className="relative flex items-center justify-between bg-accent/20 rounded-lg p-4 mb-6">
      <div className="flex items-center gap-4">
        <svg
          className="w-8 h-8 text-accent/80"
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            d="M10 2a8 8 0 100 16 8 8 0 000-16zM5.94 7.5h1.12a.75.75 0 110 1.5H5.94v1.12a.75.75 0 01-1.5 0V9h-1.12a.75.75 0 110-1.5h1.12V6.38a.75.75 0 011.5 0v1.12zm6.54 3.5a2 2 0 110-4 2 2 0 010 4zm3-2a1 1 0 11-2 0 1 1 0 012 0zm-8 6a.75.75 0 01.75-.75h4a.75.75 0 010 1.5h-4a.75.75 0 01-.75-.75z"
            clipRule="evenodd"
          />
        </svg>
        <div>
          <h3 className="font-medium text-white">Controller detected</h3>
          <p className="text-sm text-muted-foreground">
            Explore our controller compatible games
          </p>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <Link
          href="/controller"
          className="bg-accent hover:bg-accent/90 text-white rounded-full px-4 py-1.5 text-sm font-medium"
        >
          Explore games
        </Link>

        {onClose && (
          <button
            onClick={onClose}
            className="text-muted-foreground hover:text-white transition-colors"
            aria-label="Close banner"
          >
            <X size={20} />
          </button>
        )}
      </div>
    </div>
  );
};

export default ControllerBanner;
