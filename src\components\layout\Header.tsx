"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { Search, Menu, Swords, Puzzle, Car, Users, Gamepad2, Grid, Clock, Heart, Loader2 } from 'lucide-react';
import { gameApi, withRetry } from '@/lib/api';

// Interface for API category data
interface ApiCategory {
  id: number;
  name: string;
  slug: string;
  count: number;
  image?: string; // Optional fields
  show_home?: number;
}

// Define the structure for our navigation links
interface NavLink {
  name: string;
  href: string;
  icon: React.ReactNode;
}

// Helper function to map category name to icon
const getCategoryIcon = (categoryName: string): React.ReactNode => {
  const lowerCaseName = categoryName.toLowerCase();
  // Basic mapping - expand as needed based on actual API category names
  if (lowerCaseName.includes('action')) return <Swords size={16} />;
  if (lowerCaseName.includes('puzzle')) return <Puzzle size={16} />;
  if (lowerCaseName.includes('racing')) return <Car size={16} />;
  if (lowerCaseName.includes('multiplayer')) return <Users size={16} />;
  if (lowerCaseName.includes('io') || lowerCaseName.includes('io games')) return <Gamepad2 size={16} />;
  // Add more mappings here...
  // Default icon if no match
  return <Gamepad2 size={16} />; 
};

interface SearchProps {
  defaultQuery?: string;
}

const Header: React.FC<SearchProps> = ({ defaultQuery = '' }) => {
  const [searchQuery, setSearchQuery] = useState(defaultQuery);
  const router = useRouter();
  const pathname = usePathname();
  const [navLinks, setNavLinks] = useState<NavLink[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [recentCount, setRecentCount] = useState(0);
  const [favoriteCount, setFavoriteCount] = useState(0);

  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      try {
        // 使用withRetry包装API调用，允许失败后重试
        const result = await withRetry(() => gameApi.getCategories(), 2, 1000);

        if (result.status === 'success' && Array.isArray(result.data)) {
          const fetchedLinks = result.data
            .map((category): NavLink => ({
              name: category.name,
              // 使用API返回的slug构建href
              href: `/category/${category.slug || category.name.toLowerCase().replace(/\s+/g, '-')}`,
              icon: getCategoryIcon(category.name)
            }));
          setNavLinks(fetchedLinks);
        } else {
          console.error("Failed to fetch or parse categories:", result);
          // 如果失败，使用默认分类列表作为备用
          setNavLinks(getDefaultCategories());
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        // 使用默认分类列表作为备用
        setNavLinks(getDefaultCategories());
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []); // Empty dependency array means this runs once on mount

  // 默认分类列表，作为API请求失败时的备用
  const getDefaultCategories = (): NavLink[] => {
    return [
      { name: 'Action', href: '/category/action-games', icon: <Swords size={16} /> },
      { name: 'Puzzle', href: '/category/puzzle-games', icon: <Puzzle size={16} /> },
      { name: 'Racing', href: '/category/racing-games', icon: <Car size={16} /> },
      { name: 'Multiplayer', href: '/category/multiplayer-games', icon: <Users size={16} /> },
      { name: 'Arcade', href: '/category/arcade-games', icon: <Gamepad2 size={16} /> },
    ];
  };

  // 加载最近游戏和收藏游戏的数量
  useEffect(() => {
    const updateCounts = () => {
      try {
        // 读取最近游戏数量
        const recentGames = localStorage.getItem('recentGames');
        if (recentGames) {
          setRecentCount(JSON.parse(recentGames).length);
        }

        // 读取收藏游戏数量
        const favorites = localStorage.getItem('favoriteGames');
        if (favorites) {
          setFavoriteCount(JSON.parse(favorites).length);
        }
      } catch (error) {
        console.error('Error reading from localStorage:', error);
      }
    };

    updateCounts();

    // 监听storage事件，当其他页面更新游戏记录时刷新
    window.addEventListener('storage', updateCounts);
    
    return () => {
      window.removeEventListener('storage', updateCounts);
    };
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <header className="sticky top-0 z-30 flex flex-col bg-card border-b border-border">
      <div className="flex items-center justify-between px-4 py-2">
      <div className="flex items-center gap-4">
        <button className="md:hidden nav-icon">
          <Menu size={20} />
        </button>

          <Link href="/" className="logo flex items-center">
          <Image
              src="/images/logo.png"
              alt="Planet Clicker Logo"
            width={36}
            height={36}
            className="h-9 w-9"
              onError={(e) => {
                // 图片加载失败时，使用备用图片或空白
                e.currentTarget.src = 'data:image/svg+xml;charset=utf-8,%3Csvg width="36" height="36" xmlns="http://www.w3.org/2000/svg"%3E%3Crect width="100%25" height="100%25" fill="%23333"%3E%3C/rect%3E%3Ctext x="50%25" y="50%25" dominant-baseline="middle" text-anchor="middle" font-family="sans-serif" font-size="14px" fill="%23fff"%3EPC%3C/text%3E%3C/svg%3E';
              }}
          />
          <div className="flex flex-col">
              <span className="text-md font-bold">Planet Clicker</span>
          </div>
        </Link>
      </div>

        <form onSubmit={handleSearch} className="search-bar mx-4 flex-1 max-w-md hidden md:flex items-center bg-background rounded-full px-3 py-1.5">
        <Search size={18} className="text-muted-foreground" />
        <input
          type="text"
          placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-transparent border-none outline-none w-full text-sm px-2"
        />
        </form>

        <div className="flex items-center">
          <Link href="/favorites" className="nav-icon mr-4 relative">
            <Heart size={20} />
            {favoriteCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-primary text-[10px] font-bold rounded-full w-4 h-4 flex items-center justify-center text-white">
                {favoriteCount > 9 ? '9+' : favoriteCount}
              </span>
            )}
          </Link>
          <Link href="/recent" className="flex mr-2 items-center gap-1.5 text-sm font-medium text-muted-foreground hover:text-foreground relative">
            <Clock size={18} />
            {recentCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-primary text-[10px] font-bold rounded-full w-4 h-4 flex items-center justify-center text-white">
                {recentCount > 9 ? '9+' : recentCount}
              </span>
            )}
          </Link>
        </div>
      </div>

      {/* Navigation links - now with fixed height and no scrollbar */}
      <nav className="hidden md:flex px-4 py-1 border-t border-border h-[41px] overflow-hidden">
        <div className="flex items-center space-x-6 pl-14 flex-nowrap">
          {isLoading ? (
             <div className="flex items-center justify-center w-full py-2">
               <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
             </div>
          ) : (
            <>
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`whitespace-nowrap text-sm font-medium py-2 border-b-2 flex items-center gap-1.5 ${
                    pathname === link.href 
                      ? 'border-primary text-white' 
                      : 'border-transparent text-muted-foreground hover:text-foreground'
                  }`}
                >
                  {link.icon}
                  {link.name}
        </Link>
              ))}
        <Link
                href="/categories"
                className={`whitespace-nowrap text-sm font-medium py-2 border-b-2 flex items-center gap-1.5 ${
                  pathname === '/categories'
                    ? 'border-primary text-white'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
        >
                <Grid size={16} />
                All Categories
        </Link>
            </>
          )}
        </div>
      </nav>
      
      {/* Mobile search bar */}
      <div className="md:hidden px-4 py-3 w-full">
        <form onSubmit={handleSearch} className="search-bar w-full flex items-center bg-background rounded-full px-3 py-1.5">
          <Search size={18} className="text-muted-foreground" />
          <input
            type="text"
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-transparent border-none outline-none w-full text-sm px-2"
          />
        </form>
      </div>
    </header>
  );
};

export default Header;
