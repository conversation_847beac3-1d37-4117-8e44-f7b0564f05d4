"use client";

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Layout from '@/components/layout/Layout';
import GameCard from '@/components/game/GameCard';
import { Search } from 'lucide-react';
import Link from 'next/link';
import type { GameCardProps } from '@/components/game/GameCard';
import { gameApi, ApiGame } from '@/lib/api';

// 每页显示游戏数量
const GAMES_PER_PAGE = 20;

// 将API游戏数据转换为GameCardProps
function mapApiGameToGameCard(game: ApiGame): GameCardProps {
  return {
    id: game.game_id.toString(),
    title: game.name,
    slug: game.game_name,
    image: game.image,
    // 可以添加更多属性
  };
}

// 搜索结果组件
function SearchResults() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const pageParam = searchParams.get('page') || '1';
  const currentPage = parseInt(pageParam, 10);
  
  const [searchQuery, setSearchQuery] = useState(query);
  const [results, setResults] = useState<GameCardProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  // 从API获取搜索结果
  const performSearch = async (q: string, page: number) => {
    if (!q.trim()) {
      setResults([]);
      setTotalResults(0);
      setTotalPages(1);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      console.log(`Searching for: "${q}" (Page ${page})`);
      
      const data = await gameApi.searchGames(q, page, GAMES_PER_PAGE);
      console.log(`API响应状态: ${data.status}, 找到游戏数量: ${data.data?.length || 0}, 总数: ${data.total || 0}`);
      
      if (data.status === 'success' && Array.isArray(data.data)) {
        // 将API数据映射到组件需要的格式
        const mappedResults = data.data.map(mapApiGameToGameCard);
        
        setResults(mappedResults);
        setTotalResults(data.total || 0);
        setTotalPages(Math.ceil((data.total || 0) / GAMES_PER_PAGE));
      } else {
        setResults([]);
        setError('无法解析搜索结果');
        setTotalPages(1);
      }
    } catch (err) {
      console.error('搜索时发生错误:', err);
      setError('搜索时发生错误，请稍后再试');
      setResults([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (query) {
      performSearch(query, currentPage);
    }
  }, [query, currentPage]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Update URL query params, reset to first page
    const url = new URL(window.location.href);
    url.searchParams.set('q', searchQuery);
    url.searchParams.set('page', '1');
    window.history.pushState({}, '', url);
    
    performSearch(searchQuery, 1);
  };

  // Pagination component
  const Pagination = () => {
    if (totalPages <= 1) return null;
    
    return (
      <div className="flex justify-center mt-8 mb-4">
        <div className="flex flex-wrap justify-center gap-1">
          {currentPage > 1 && (
            <Link
              href={`/search?q=${encodeURIComponent(query)}&page=${currentPage - 1}`}
              className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
            >
              Prev
            </Link>
          )}
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // Display page numbers around current page
            let pageNum = 1;
            if (totalPages <= 5) {
              // If total pages are less than 5, show all page numbers
              pageNum = i + 1;
            } else if (currentPage <= 3) {
              // If current page is in first 3 pages, show pages 1-5
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              // If current page is in last 3 pages, show last 5 pages
              pageNum = totalPages - 4 + i;
            } else {
              // Otherwise show 2 pages before and after current page
              pageNum = currentPage - 2 + i;
            }
            
            // Hide some page numbers on smallest screens
            const hideOnMobile = (i === 0 || i === 4) && totalPages > 3;
            
            return (
              <Link
                key={pageNum}
                href={`/search?q=${encodeURIComponent(query)}&page=${pageNum}`}
                className={`${hideOnMobile ? 'hidden sm:block' : ''} px-2 sm:px-4 py-2 text-sm sm:text-base rounded ${
                  currentPage === pageNum
                    ? 'bg-primary text-white'
                    : 'bg-card hover:bg-card/80 text-muted-foreground hover:text-white'
                }`}
              >
                {pageNum}
              </Link>
            );
          })}
          
          {currentPage < totalPages && (
            <Link
              href={`/search?q=${encodeURIComponent(query)}&page=${currentPage + 1}`}
              className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
            >
              Next
            </Link>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-screen-xl mx-auto px-4">
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-bold mb-6">Search Games</h1>
        
        <form onSubmit={handleSearch} className="mb-8">
          <div className="flex w-full max-w-2xl">
            <div className="relative flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search for games..."
                className="bg-card border border-border rounded-l py-2 px-4 w-full focus:outline-none focus:ring-1 focus:ring-primary"
              />
            </div>
            <button
              type="submit"
              className="bg-primary hover:bg-primary/90 text-white rounded-r px-4 py-2 flex items-center"
            >
              <Search size={20} />
            </button>
          </div>
        </form>
        
        {query && !error && (
          <p className="text-muted-foreground mb-4">
            {loading 
              ? 'Searching...' 
              : `Found ${totalResults} ${totalResults === 1 ? 'result' : 'results'} for "${query}"`
            }
            {totalResults > GAMES_PER_PAGE && !loading && ` - Page ${currentPage} of ${totalPages}`}
          </p>
        )}
        
        {error && (
          <p className="text-red-500 mb-4">{error}</p>
        )}
      </div>

      {loading ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {[...Array(10)].map((_, i) => (
            <div key={i} className="bg-card animate-pulse rounded-lg overflow-hidden">
              <div className="aspect-video bg-gray-700" />
              <div className="p-3">
                <div className="h-4 bg-gray-700 rounded w-3/4 mb-2" />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <>
          {results.length > 0 ? (
            <>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {results.map((game) => (
                  <GameCard key={game.id} {...game} />
                ))}
              </div>
              
              <Pagination />
            </>
          ) : (
            query && !loading && (
              <div className="text-center py-12">
                <p className="text-xl mb-2">No results found for "{query}"</p>
                <p className="text-muted-foreground">Try different keywords or check your spelling</p>
              </div>
            )
          )}
        </>
      )}
    </div>
  );
}

// 导出页面组件，使用Suspense包装搜索结果
export default function SearchPage() {
  return (
    <Layout>
      <Suspense fallback={
        <div className="max-w-screen-xl mx-auto px-4 py-12 text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
          </div>
          <p className="mt-4 text-muted-foreground">Loading search results...</p>
        </div>
      }>
        <SearchResults />
      </Suspense>
    </Layout>
  );
} 