import './globals.css';
import type { Metadata } from 'next';
import Script from 'next/script';
import ClientBody from "./ClientBody";

// 使用系统字体堆栈替代Google字体
const systemFontStack = {
  variable: "--font-inter",
  className: "font-sans",
};

export const metadata: Metadata = {
  title: 'Free Online Games on Planet Clicker | Play Now!',
  description: 'Play free online games at Planet Clicker, the best place to play high-quality browser games. We add new games every day. Have fun!',
  keywords: 'games, online games, browser games, free games, fun games, play games, io games, multiplayer games',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${systemFontStack.variable}`}>
      <head>
        {/* 百度统计代码 */}
        <Script id="baidu-analytics" strategy="afterInteractive">
          {`
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?cb7794f0c9544302072b8ae5e0548453";
              var s = document.getElementsByTagName("script")[0]; 
              s.parentNode.insertBefore(hm, s);
            })();
          `}
        </Script>
        <style>{`
          /* 定义系统字体堆栈 */
          :root {
            --font-inter: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
          }
          .prose {
            color: rgb(229 231 235); /* text-gray-200 */
          }
          .prose a {
            color: rgb(59 130 246); /* text-blue-500 */
            text-decoration: underline;
          }
          .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
            color: rgb(255 255 255); /* text-white */
            font-weight: 600;
            margin-top: 1.5em;
            margin-bottom: 0.75em;
          }
          .prose p, .prose ul, .prose ol {
            margin-bottom: 1.25em;
          }
          .prose strong {
            color: rgb(255 255 255); /* text-white */
            font-weight: 600;
          }
          .prose ul {
            list-style-type: disc;
            padding-left: 1.625em;
          }
          .prose ol {
            list-style-type: decimal;
            padding-left: 1.625em;
          }
          .prose blockquote {
            font-style: italic;
            border-left: 4px solid rgb(75 85 99); /* border-gray-600 */
            padding-left: 1em;
            margin-left: 0;
            margin-right: 0;
            color: rgb(156 163 175); /* text-gray-400 */
          }
          .prose code {
            color: rgb(249 115 22); /* text-orange-500 */
            background-color: rgb(31 41 55); /* bg-gray-800 */
            padding: 0.2em 0.4em;
            border-radius: 0.25em;
            font-size: 0.875em;
          }
          .prose pre {
            background-color: rgb(31 41 55); /* bg-gray-800 */
            overflow-x: auto;
            padding: 1em;
            border-radius: 0.375em;
            margin-top: 1.25em;
            margin-bottom: 1.25em;
          }
        `}</style>
      </head>
      <ClientBody>{children}</ClientBody>
    </html>
  );
}
