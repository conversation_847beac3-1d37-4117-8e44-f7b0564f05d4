"use client";

import type React from 'react';
import Link from 'next/link';
import GameCard, { type GameCardProps } from './GameCard';

interface GameSectionProps {
  title: string;
  viewMoreLink?: string;
  games: GameCardProps[];
  className?: string;
  featuredIndex?: number; // Index of the game to be featured (larger)
  compact?: boolean; // 新增: 用于显示小尺寸的游戏卡片
}

const GameSection: React.FC<GameSectionProps> = ({
  title,
  viewMoreLink,
  games,
  className = '',
  featuredIndex,
  compact = false,
}) => {
  if (games.length === 0) return null;

  return (
    <section className={`game-section ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="game-section-title">{title}</h2>
        {viewMoreLink && (
          <Link href={viewMoreLink} className="text-muted-foreground hover:text-white text-sm">
            View more
          </Link>
        )}
      </div>
      <div className={`${compact ? 'game-grid-compact' : 'game-grid'}`}>
        {games.map((game, index) => (
          <GameCard
            key={game.id}
            {...game}
            size={compact ? 'compact' : featuredIndex === index ? 'large' : 'normal'}
          />
        ))}
      </div>
    </section>
  );
};

export default GameSection;
