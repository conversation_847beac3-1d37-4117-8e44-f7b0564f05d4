import React from 'react';
import { Gamepad2, MonitorPlay, DownloadCloud, Laptop, Users, PercentCircle } from 'lucide-react';

interface FeatureIconProps {
  type: 'games' | 'install' | 'device' | 'friends' | 'free';
  size?: number;
}

const FeatureIcon = ({ type, size = 24 }: FeatureIconProps) => {
  switch (type) {
    case 'games':
      return <Gamepad2 size={size} className="text-primary" />;
    case 'install':
      return <DownloadCloud size={size} className="text-primary" />;
    case 'device':
      return <Laptop size={size} className="text-primary" />;
    case 'friends':
      return <Users size={size} className="text-primary" />;
    case 'free':
      return <PercentCircle size={size} className="text-primary" />;
    default:
      return null;
  }
};

export default FeatureIcon; 