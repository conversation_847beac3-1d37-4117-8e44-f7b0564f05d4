import React from 'react';

export interface AdPlaceholderProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  type?: 'sidebar' | 'banner' | 'rectangle';
  children?: React.ReactNode;
}

/**
 * 广告占位组件，用于显示广告位
 */
export default function AdPlaceholder({
  width,
  height,
  className = '',
  type = 'rectangle',
  children
}: AdPlaceholderProps) {
  let defaultWidth = '300px';
  let defaultHeight = '250px';
  
  // 根据广告类型设置默认尺寸
  switch (type) {
    case 'sidebar':
      defaultWidth = '160px';
      defaultHeight = '600px';
      break;
    case 'banner':
      defaultWidth = '728px';
      defaultHeight = '90px';
      break;
    case 'rectangle':
    default:
      defaultWidth = '300px';
      defaultHeight = '250px';
      break;
  }
  
  const finalWidth = width || defaultWidth;
  const finalHeight = height || defaultHeight;
  
  return (
    <div 
      className={`border border-gray-600 bg-background rounded-lg flex items-center justify-center ${className}`}
      style={{ 
        width: finalWidth,
        height: finalHeight,
        minWidth: finalWidth,
        minHeight: finalHeight,
      }}
    >
      {children || (
        <div className="text-center p-4 text-muted-foreground">
          <div className="text-lg font-medium">Advertisement</div>
          <div className="text-xs">{typeof finalWidth === 'number' ? `${finalWidth}×${finalHeight}` : `${finalWidth} × ${finalHeight}`}</div>
        </div>
      )}
    </div>
  );
} 