"use client";

import type React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

export interface GameCardProps {
  id: string;
  title: string;
  image: string;
  slug: string;
  badge?: 'hot' | 'new' | 'top-rated' | 'updated' | null;
  size?: 'normal' | 'large' | 'compact';
}

const badgeComponents = {
  hot: <span className="hot-badge">HOT</span>,
  new: <span className="new-badge">NEW</span>,
  'top-rated': <span className="top-rated-badge">TOP RATED</span>,
  updated: <span className="updated-badge">UPDATED</span>,
};

// 生成带游戏标题的占位图像
const generateFallbackImage = (title: string, width: number, height: number): string => {
  // 为标题生成颜色（基于标题字符的简单hash）
  const getColorFromTitle = (title: string): string => {
    const hash = title.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const hue = hash % 360; // 0-359的色相
    return `hsl(${hue}, 70%, 40%)`;
  };
  
  const color = getColorFromTitle(title);
  const shortTitle = title.length > 5 ? title.slice(0, 5) : title;
  
  // 注意：转义SVG文本用于在data URI中使用
  const escapedTitle = shortTitle.replace(/[<>&'"]/g, c => {
    return { '<': '&lt;', '>': '&gt;', '&': '&amp;', "'": '&apos;', '"': '&quot;' }[c] || c;
  });
  
  return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}"><rect width="100%" height="100%" fill="${color}"/><text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${width/10}px" fill="white" text-anchor="middle" dominant-baseline="middle">${escapedTitle}</text></svg>`;
};

const GameCard: React.FC<GameCardProps> = ({
  id,
  title,
  image,
  slug,
  badge = null,
  size = 'normal'
}) => {
  const [imgSrc, setImgSrc] = useState(image);
  const [imgError, setImgError] = useState(false);
  
  // 图片尺寸计算
  const imgWidth = size === 'large' ? 675 : size === 'compact' ? 180 : 273;
  const imgHeight = size === 'large' ? 380 : size === 'compact' ? 101 : 153;
  
  // 图片加载错误处理
  const handleImageError = () => {
    // 防止无限重试
    if (!imgError) {
      setImgError(true);
      // 使用生成的占位图像
      setImgSrc(generateFallbackImage(title, imgWidth, imgHeight));
    }
  };

  return (
    <Link
      href={`/game/${slug}`}
      key={id}
      className={`game-card block bg-card overflow-hidden rounded-lg ${
        size === 'large' ? 'col-span-2 row-span-2' : 
        size === 'compact' ? 'compact-card' : ''
      }`}
    >
      <div className="relative">
        <Image
          src={imgSrc}
          alt={title}
          width={imgWidth}
          height={imgHeight}
          className="w-full h-auto object-cover"
          priority={size === 'large'}
          onError={handleImageError}
        />
        {badge && (
          <div className={`absolute top-1 right-1 sm:top-2 sm:right-2 ${size === 'compact' ? 'compact-badge' : ''}`}>
            {badgeComponents[badge]}
          </div>
        )}
        <div className="absolute bottom-0 left-0 w-full p-1 sm:p-2 bg-gradient-to-t from-black/80 to-transparent">
          <h3 className={`text-white font-medium truncate ${
            size === 'compact' ? 'text-xs' : 'text-xs sm:text-sm md:text-base'
          }`}>
            {title}
          </h3>
        </div>
      </div>
    </Link>
  );
};

export default GameCard;
