import Layout from '@/components/layout/Layout';
import GameCard from '@/components/game/GameCard';
import type { GameCardProps } from '@/components/game/GameCard';
import { notFound } from 'next/navigation';
import type { Metadata } from 'next';
import Link from 'next/link';

// 定义分类信息和描述
const categoryDescriptions: Record<string, string> = {
  'action-games': 'Fast-paced adventure games requiring quick reflexes and precision',
  'puzzle-games': 'Games that test your intelligence and problem-solving abilities',
  'racing-games': 'Race with various vehicles on different tracks',
  'multiplayer-games': 'Games to play with friends or players around the world',
  'shooting-games': 'Aim, shoot, and hit targets in these exciting games',
  'arcade-games': 'Classic arcade-style games with simple mechanics and addictive gameplay',
  'io-games': 'Simple but addictive multiplayer online games',
  'platform-games': 'Jump and run through various obstacles and levels',
  'strategy-games': 'Plan, think ahead, and outsmart your opponents',
  'sports-games': 'Simulate various sports and compete against others',
};

// 定义每页显示游戏数量
const GAMES_PER_PAGE = 20;

// 将分类名转换为展示名称
function getCategoryDisplayName(categorySlug: string): string {
  return categorySlug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// 将分类名转换为API参数格式（空格替代连字符）
function getCategoryApiName(categorySlug: string): string {
  return categorySlug.replace(/-/g, ' ');
}

interface CategoryPageProps {
  params: {
    name: string;
  };
  searchParams: {
    page?: string;
  };
}

// 定义API响应结构
interface ApiResponse {
  status: string;
  data: ApiGame[];
  total?: number;
}

interface ApiGame {
  game_id: number;
  game_name: string; // slug
  name: string;      // title
  image: string;
  // 其他可能的字段
}

// Pagination component
function Pagination({ 
  currentPage, 
  totalPages, 
  categoryName 
}: { 
  currentPage: number; 
  totalPages: number; 
  categoryName: string;
}) {
  return (
    <div className="flex justify-center mt-8 mb-4">
      <div className="flex flex-wrap justify-center gap-1">
        {currentPage > 1 && (
          <Link
            href={`/category/${categoryName}?page=${currentPage - 1}`}
            className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
          >
            Prev
          </Link>
        )}
        
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          // Display page numbers around current page
          let pageNum = 1;
          if (totalPages <= 5) {
            // If total pages are less than 5, show all page numbers
            pageNum = i + 1;
          } else if (currentPage <= 3) {
            // If current page is in first 3 pages, show pages 1-5
            pageNum = i + 1;
          } else if (currentPage >= totalPages - 2) {
            // If current page is in last 3 pages, show last 5 pages
            pageNum = totalPages - 4 + i;
          } else {
            // Otherwise show 2 pages before and after current page
            pageNum = currentPage - 2 + i;
          }
          
          // Hide some page numbers on smallest screens
          const hideOnMobile = (i === 0 || i === 4) && totalPages > 3;
          
          return (
            <Link
              key={pageNum}
              href={`/category/${categoryName}?page=${pageNum}`}
              className={`${hideOnMobile ? 'hidden sm:block' : ''} px-2 sm:px-4 py-2 text-sm sm:text-base rounded ${
                currentPage === pageNum
                  ? 'bg-primary text-white'
                  : 'bg-card hover:bg-card/80 text-muted-foreground hover:text-white'
              }`}
            >
              {pageNum}
            </Link>
          );
        })}
        
        {currentPage < totalPages && (
          <Link
            href={`/category/${categoryName}?page=${currentPage + 1}`}
            className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
          >
            Next
          </Link>
        )}
      </div>
    </div>
  );
}

// 从API获取分类游戏列表（带分页）
async function fetchCategoryGames(
  categoryName: string, 
  page: number = 1
): Promise<{ games: GameCardProps[]; total: number }> {
  try {
    console.log(`Fetching games for category: ${categoryName} (page ${page})`);
    const apiBaseUrl = 'https://api.planetclicker.pro';
    const apiCategoryName = getCategoryApiName(categoryName);
    
    // 添加分页参数
    const url = `${apiBaseUrl}/games/category/name/${encodeURIComponent(apiCategoryName)}?page=${page}&limit=${GAMES_PER_PAGE}`;
    console.log(`API endpoint: ${url}`);
    
    const response = await fetch(url);
    
    if (!response.ok) {
      console.error(`Failed to fetch category games: ${response.status}`);
      return { games: [], total: 0 };
    }
    
    const result: ApiResponse = await response.json();
    
    if (result.status === 'success' && Array.isArray(result.data)) {
      console.log(`Successfully fetched ${result.data.length} games for category: ${categoryName}`);
      const games = result.data.map((game): GameCardProps => ({
        id: game.game_id.toString(),
        title: game.name,
        slug: game.game_name,
        image: game.image,
      }));
      
      // 返回游戏数据和总数（如果API提供）
      return { 
        games,
        total: result.total || games.length 
      };
    } else {
      console.error(`Failed to parse category games for ${categoryName}:`, result);
      return { games: [], total: 0 };
    }
  } catch (error) {
    console.error(`Error fetching category games for ${categoryName}:`, error);
    return { games: [], total: 0 };
  }
}

export async function generateMetadata(
  { params }: CategoryPageProps
): Promise<Metadata> {
  // 确保 params 已经解析完成
  const resolvedParams = await Promise.resolve(params);
  const categoryName = resolvedParams.name;
  
  const categoryDisplayName = getCategoryDisplayName(categoryName);
  const description = categoryDescriptions[categoryName] || `Play the best ${categoryDisplayName} online for free`;

  return {
    title: `${categoryDisplayName} - Play Online for Free | H5Play`,
    description: description,
  };
}

export default async function CategoryPage(
  { params, searchParams }: CategoryPageProps
) {
  // 确保 params 已经解析完成
  const resolvedParams = await Promise.resolve(params);
  const categoryName = resolvedParams.name;
  
  // 确保 searchParams 已经解析完成
  const resolvedSearchParams = await Promise.resolve(searchParams);
  const pageParam = resolvedSearchParams.page || '1';
  const page = parseInt(pageParam, 10);
  
  // 获取游戏列表和总数
  const { games, total } = await fetchCategoryGames(categoryName, page);
  const totalPages = Math.ceil(total / GAMES_PER_PAGE);
  
  const categoryDisplayName = getCategoryDisplayName(categoryName);
  const description = categoryDescriptions[categoryName] || `Play the best ${categoryDisplayName} online for free`;

  return (
    <Layout>
      <div className="max-w-screen-xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-white">{categoryDisplayName}</h1>
          <p className="text-muted-foreground mt-2">{description}</p>
        </div>

        {games.length > 0 ? (
          <>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {games.map((game: GameCardProps) => (
                <GameCard
                  key={game.id}
                  {...game}
                />
              ))}
            </div>
            
            {/* 添加分页组件 */}
            {totalPages > 1 && (
              <Pagination 
                currentPage={page} 
                totalPages={totalPages} 
                categoryName={categoryName} 
              />
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-muted-foreground">No games found in this category</h3>
            <p className="mt-2">Check back later for new games!</p>
          </div>
        )}
      </div>
    </Layout>
  );
} 