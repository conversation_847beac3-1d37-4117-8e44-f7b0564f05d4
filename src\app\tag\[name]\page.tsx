import Layout from '@/components/layout/Layout';
import GameCard from '@/components/game/GameCard';
import type { GameCardProps } from '@/components/game/GameCard';
import { notFound } from 'next/navigation';
import type { Metadata } from 'next';
import Link from 'next/link';

// 定义每页显示游戏数量
const GAMES_PER_PAGE = 20;

// 常见标签的描述信息
const tagDescriptions: Record<string, string> = {
  'action': 'Fast-paced games with exciting gameplay and quick reflexes',
  'adventure': 'Games focused on exploration, puzzle-solving, and storytelling',
  'puzzle': 'Brain-teasing games that challenge your problem-solving skills',
  'strategy': 'Games that require planning, tactical thinking, and resource management',
  'racing': 'High-speed games where you compete to be the fastest',
  'shooting': 'Games where accuracy and quick reactions are key to hitting targets',
  'multiplayer': 'Games you can play with friends or other players online',
  'platform': 'Jump and run through various obstacles and levels',
  'sports': 'Games simulating real-world sports and athletic competition',
  'arcade': 'Classic-style games with simple mechanics and addictive gameplay',
};

// 美化标签名称显示
function getTagDisplayName(tagSlug: string): string {
  return tagSlug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// 转换标签名称为API参数格式
function getTagApiName(tagSlug: string): string {
  return tagSlug.replace(/-/g, ' ');
}

interface TagPageProps {
  params: {
    name: string;
  };
  searchParams: {
    page?: string;
  };
}

// 定义API响应结构
interface ApiResponse {
  status: string;
  data: ApiGame[];
  total?: number;
}

interface ApiGame {
  game_id: number;
  game_name: string; // slug
  name: string;      // title
  image: string;
  // 其他可能的字段
}

// 分页组件
function Pagination({ 
  currentPage, 
  totalPages, 
  tagName 
}: { 
  currentPage: number; 
  totalPages: number; 
  tagName: string;
}) {
  return (
    <div className="flex justify-center mt-8 mb-4">
      <div className="flex flex-wrap justify-center gap-1">
        {currentPage > 1 && (
          <Link
            href={`/tag/${tagName}?page=${currentPage - 1}`}
            className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
          >
            Prev
          </Link>
        )}
        
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          // Display page numbers around current page
          let pageNum = 1;
          if (totalPages <= 5) {
            pageNum = i + 1;
          } else if (currentPage <= 3) {
            pageNum = i + 1;
          } else if (currentPage >= totalPages - 2) {
            pageNum = totalPages - 4 + i;
          } else {
            pageNum = currentPage - 2 + i;
          }
          
          // Hide some page numbers on smallest screens
          const hideOnMobile = (i === 0 || i === 4) && totalPages > 3;
          
          return (
            <Link
              key={pageNum}
              href={`/tag/${tagName}?page=${pageNum}`}
              className={`${hideOnMobile ? 'hidden sm:block' : ''} px-2 sm:px-4 py-2 text-sm sm:text-base rounded ${
                currentPage === pageNum
                  ? 'bg-primary text-white'
                  : 'bg-card hover:bg-card/80 text-muted-foreground hover:text-white'
              }`}
            >
              {pageNum}
            </Link>
          );
        })}
        
        {currentPage < totalPages && (
          <Link
            href={`/tag/${tagName}?page=${currentPage + 1}`}
            className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
          >
            Next
          </Link>
        )}
      </div>
    </div>
  );
}

// 从API获取标签游戏列表
async function fetchTagGames(
  tagName: string, 
  page: number = 1
): Promise<{ games: GameCardProps[]; total: number }> {
  try {
    console.log(`Fetching games for tag: ${tagName} (page ${page})`);
    const apiBaseUrl = 'https://api.planetclicker.pro';
    const apiTagName = getTagApiName(tagName);
    
    // 添加分页参数
    const url = `${apiBaseUrl}/games/tag/name/${encodeURIComponent(apiTagName)}?page=${page}&limit=${GAMES_PER_PAGE}`;
    console.log(`API endpoint: ${url}`);
    
    const response = await fetch(url);
    
    if (!response.ok) {
      console.error(`Failed to fetch tag games: ${response.status}`);
      return { games: [], total: 0 };
    }
    
    const result: ApiResponse = await response.json();
    
    if (result.status === 'success' && Array.isArray(result.data)) {
      console.log(`Successfully fetched ${result.data.length} games for tag: ${tagName}`);
      const games = result.data.map((game): GameCardProps => ({
        id: game.game_id.toString(),
        title: game.name,
        slug: game.game_name,
        image: game.image,
      }));
      
      // 返回游戏数据和总数
      return { 
        games,
        total: result.total || games.length 
      };
    } else {
      console.error(`Failed to parse tag games for ${tagName}:`, result);
      return { games: [], total: 0 };
    }
  } catch (error) {
    console.error(`Error fetching tag games for ${tagName}:`, error);
    return { games: [], total: 0 };
  }
}

export async function generateMetadata(
  { params }: TagPageProps
): Promise<Metadata> {
  // 确保 params 已经解析完成
  const resolvedParams = await Promise.resolve(params);
  const tagName = resolvedParams.name;
  
  const tagDisplayName = getTagDisplayName(tagName);
  const tagKey = tagName.toLowerCase().split('-')[0]; // 获取第一个单词作为键
  const description = tagDescriptions[tagKey] || `Play the best ${tagDisplayName} games online for free`;

  return {
    title: `${tagDisplayName} Games - Play Online for Free | Planet Clicker`,
    description: description,
  };
}

export default async function TagPage(
  { params, searchParams }: TagPageProps
) {
  // 确保 params 已经解析完成
  const resolvedParams = await Promise.resolve(params);
  const tagName = resolvedParams.name;
  
  // 确保 searchParams 已经解析完成
  const resolvedSearchParams = await Promise.resolve(searchParams);
  const pageParam = resolvedSearchParams.page || '1';  
  const page = parseInt(pageParam, 10);
  
  // 获取游戏列表和总数
  const { games, total } = await fetchTagGames(tagName, page);
  const totalPages = Math.ceil(total / GAMES_PER_PAGE);
  
  const tagDisplayName = getTagDisplayName(tagName);
  const tagKey = tagName.toLowerCase().split('-')[0]; // 获取第一个单词作为键
  const description = tagDescriptions[tagKey] || `Play the best ${tagDisplayName} games online for free`;

  return (
    <Layout>
      <div className="max-w-screen-xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-white">{tagDisplayName} Games</h1>
          <p className="text-muted-foreground mt-2">{description}</p>
        </div>

        {games.length > 0 ? (
          <>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {games.map((game: GameCardProps) => (
                <GameCard
                  key={game.id}
                  {...game}
                />
              ))}
            </div>
            
            {/* 添加分页组件 */}
            {totalPages > 1 && (
              <Pagination 
                currentPage={page} 
                totalPages={totalPages} 
                tagName={tagName} 
              />
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-muted-foreground">No games found with this tag</h3>
            <p className="mt-2">Check back later for new games!</p>
          </div>
        )}
      </div>
    </Layout>
  );
} 