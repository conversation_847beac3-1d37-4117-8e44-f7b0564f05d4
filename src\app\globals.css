@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 星空主题颜色 - 深蓝紫色调 */
    --background: 230 35% 7%;
    --foreground: 210 40% 98%;
    --card: 235 30% 12%;
    --card-foreground: 210 40% 98%;
    --popover: 235 30% 12%;
    --popover-foreground: 210 40% 98%;
    --primary: 260 85% 65%;
    --primary-foreground: 210 40% 98%;
    --secondary: 240 25% 18%;
    --secondary-foreground: 210 40% 98%;
    --muted: 240 25% 18%;
    --muted-foreground: 240 15% 75%;
    --accent: 280 85% 70%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 240 20% 25%;
    --input: 240 20% 25%;
    --ring: 260 85% 65%;
    --radius: 0.5rem;
    --font-inter: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    /* 星空主题特殊颜色 */
    --star-glow: 60 100% 85%;
    --nebula-purple: 270 80% 60%;
    --nebula-blue: 220 85% 65%;
    --cosmic-gold: 45 100% 70%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
    font-family: var(--font-inter);
    background: linear-gradient(135deg,
      hsl(230, 35%, 7%) 0%,
      hsl(240, 40%, 10%) 25%,
      hsl(250, 45%, 8%) 50%,
      hsl(260, 40%, 12%) 75%,
      hsl(230, 35%, 7%) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    position: relative;
  }

  /* 星空背景效果 */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(2px 2px at 20px 30px, hsl(var(--star-glow)), transparent),
      radial-gradient(2px 2px at 40px 70px, hsl(var(--star-glow)), transparent),
      radial-gradient(1px 1px at 90px 40px, hsl(var(--star-glow)), transparent),
      radial-gradient(1px 1px at 130px 80px, hsl(var(--star-glow)), transparent),
      radial-gradient(2px 2px at 160px 30px, hsl(var(--star-glow)), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: twinkle 4s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: -1;
  }

  @keyframes twinkle {
    0% { opacity: 0.3; }
    100% { opacity: 0.8; }
  }
}

@layer components {
  .game-card {
    @apply relative overflow-hidden rounded-lg transition-all duration-300 hover:scale-105;
    background: linear-gradient(145deg,
      hsl(var(--card)) 0%,
      hsl(240, 25%, 15%) 100%);
    border: 1px solid hsl(var(--border));
    box-shadow:
      0 4px 15px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .game-card:hover {
    box-shadow:
      0 8px 25px rgba(138, 43, 226, 0.3),
      0 0 20px rgba(138, 43, 226, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: hsl(var(--primary));
  }

  .nav-icon {
    @apply flex h-6 w-6 items-center justify-center text-muted-foreground hover:text-white transition-all duration-200;
  }

  .nav-icon:hover {
    text-shadow: 0 0 8px hsl(var(--primary));
  }

  .sidebar {
    @apply fixed left-0 top-0 h-full overflow-hidden;
    background: linear-gradient(180deg,
      hsl(var(--card)) 0%,
      hsl(240, 30%, 10%) 100%);
    border-right: 1px solid hsl(var(--border));
  }

  .logo {
    @apply flex items-center gap-2 text-white font-bold;
    text-shadow: 0 0 10px hsl(var(--primary));
  }

  .game-section {
    @apply space-y-4;
  }

  .game-section-title {
    @apply text-xl font-bold text-white;
  }

  .game-grid {
    @apply grid grid-cols-3 gap-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5;
  }

  /* 为Similar Games添加更紧凑的网格布局 */
  .game-grid-compact {
    @apply grid grid-cols-4 gap-1.5 sm:grid-cols-5 md:grid-cols-7 lg:grid-cols-8 xl:grid-cols-10;
  }

  /* 小尺寸游戏卡片 */
  .compact-card {
    @apply text-xs;
  }

  /* 小尺寸游戏卡片徽章 */
  .compact-badge .hot-badge,
  .compact-badge .new-badge,
  .compact-badge .top-rated-badge,
  .compact-badge .updated-badge {
    @apply text-[8px] py-0 px-1;
  }

  /* 确保相关游戏部分不在footer下方显示 */
  .similar-games-section {
    @apply z-10 relative mb-12;
  }

  /* 相关游戏底部外边距 */
  .similar-games-section .game-grid-compact {
    @apply mb-6;
  }

  .top-rated-badge {
    @apply bg-yellow-500 text-black text-xs font-bold py-0.5 px-1 sm:px-2 rounded text-[10px] sm:text-xs;
  }

  .hot-badge {
    @apply bg-red-500 text-white text-xs font-bold py-0.5 px-1 sm:px-2 rounded text-[10px] sm:text-xs;
  }

  .updated-badge {
    @apply bg-green-500 text-white text-xs font-bold py-0.5 px-1 sm:px-2 rounded text-[10px] sm:text-xs;
  }

  .new-badge {
    @apply bg-blue-500 text-white text-xs font-bold py-0.5 px-1 sm:px-2 rounded text-[10px] sm:text-xs;
  }

  .search-bar {
    @apply rounded-full px-3 py-1.5 flex items-center gap-2 transition-all duration-300;
    background: linear-gradient(145deg,
      hsl(var(--muted)) 0%,
      hsl(240, 20%, 20%) 100%);
    border: 1px solid hsl(var(--border));
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.2),
      0 0 0 0 rgba(138, 43, 226, 0);
  }

  .search-bar:focus-within {
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.2),
      0 0 15px rgba(138, 43, 226, 0.4);
    border-color: hsl(var(--primary));
  }
  
  .home-icon {
    @apply text-primary font-bold;
  }
  
  .home-icon svg {
    @apply text-primary w-5 h-5;
  }

  /* Hover effect for sidebar items */
  .sidebar-icon {
    @apply flex items-center transition-all duration-200;
  }
  
  .sidebar-icon span {
    @apply overflow-hidden;
  }

  /* CMS页面样式优化 */
  .cms-content {
    @apply text-foreground;
  }

  .cms-content h1, 
  .cms-content h2, 
  .cms-content h3, 
  .cms-content h4 {
    @apply font-bold mt-6 mb-3;
  }

  .cms-content h1 {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .cms-content h2 {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .cms-content h3 {
    @apply text-base sm:text-lg md:text-xl;
  }

  .cms-content p {
    @apply mb-4 break-words;
  }

  .cms-content ul, 
  .cms-content ol {
    @apply pl-5 mb-4;
  }

  .cms-content li {
    @apply mb-1;
  }

  .cms-content img {
    @apply max-w-full h-auto rounded my-4 mx-auto;
  }

  .cms-content table {
    @apply w-full overflow-x-auto block mb-4 border-collapse;
  }

  .cms-content table th,
  .cms-content table td {
    @apply p-2 border border-border;
  }

  .cms-content a {
    @apply text-blue-400 hover:text-blue-300 underline;
  }

  .cms-content blockquote {
    @apply border-l-4 border-muted pl-4 italic my-4;
  }

  .cms-content pre {
    @apply bg-background p-3 rounded overflow-x-auto text-xs sm:text-sm my-4;
  }

  .cms-content code {
    @apply bg-background px-1 py-0.5 rounded text-xs sm:text-sm;
  }

  /* 星空主题特殊效果 */
  .cosmic-glow {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.5);
    animation: cosmic-pulse 2s ease-in-out infinite alternate;
  }

  @keyframes cosmic-pulse {
    0% { box-shadow: 0 0 20px rgba(138, 43, 226, 0.5); }
    100% { box-shadow: 0 0 30px rgba(138, 43, 226, 0.8); }
  }

  .nebula-bg {
    background: linear-gradient(135deg,
      hsl(var(--nebula-purple)) 0%,
      hsl(var(--nebula-blue)) 50%,
      hsl(var(--nebula-purple)) 100%);
  }

  .star-shimmer {
    position: relative;
    overflow: hidden;
  }

  .star-shimmer::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  }

  .planet-orbit {
    animation: orbit 20s linear infinite;
  }

  @keyframes orbit {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 文本阴影工具类 */
  .hover\:text-shadow-cosmic:hover {
    text-shadow: 0 0 8px hsl(var(--primary));
  }

  /* 按钮和交互元素的宇宙主题 */
  .cosmic-button {
    background: linear-gradient(135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--accent)) 100%);
    border: 1px solid hsl(var(--primary));
    box-shadow:
      0 4px 15px rgba(138, 43, 226, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .cosmic-button:hover {
    box-shadow:
      0 6px 20px rgba(138, 43, 226, 0.5),
      0 0 20px rgba(138, 43, 226, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
  }

  /* 星系背景动画 */
  .galaxy-bg {
    background: radial-gradient(ellipse at center,
      hsl(var(--nebula-purple)) 0%,
      hsl(var(--nebula-blue)) 30%,
      transparent 70%);
    animation: galaxy-rotate 30s linear infinite;
  }

  @keyframes galaxy-rotate {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
  }
}
