import Layout from '@/components/layout/Layout';
import Link from 'next/link';
import Image from 'next/image';
import { Gamepad2 } from 'lucide-react';

// 分类接口
interface Category {
  id: number;
  name: string;
  image?: string;
  show_home?: number;
}

// 游戏接口
interface Game {
  game_id: number;
  game_name: string; // slug
  name: string;      // title
  image: string;
}

// 分类API响应
interface CategoriesApiResponse {
  status: string;
  data: Category[];
}

// 游戏API响应
interface GamesApiResponse {
  status: string;
  data: Game[];
}

// 获取本地分类图片路径
function getCategoryImage(categoryName: string): string {
  const name = categoryName.toLowerCase();
  const slug = name.replace(/\s+/g, '-');
  
  // 定义可用的分类图片列表
  const availableImages = [
    'action-games', 'adventure-games', 'arcade-games', 
    'baby-hazel-games', 'bejeweled-games', 'boys-games', 
    'clicker-games', 'cooking-games', 'fighting-games', 
    'girls-games', 'hypercasual-games', 'io-games', 
    'multiplayer-games', 'puzzle-games', 'racing-games', 
    'shooting-games', 'soccer-games', 'sports-games', 
    'stickman-games', 'strategy-games', 'two-player-games', '3d-games'
  ];
  
  // 检查是否有直接匹配的图片
  if (availableImages.includes(slug)) {
    return `/images/cat/${slug}.jpg`;
  }
  
  // 如果没有精确匹配，尝试查找部分匹配
  for (const imageSlug of availableImages) {
    if (slug.includes(imageSlug.replace('-games', '')) || 
        imageSlug.includes(slug.replace('-games', ''))) {
      return `/images/cat/${imageSlug}.jpg`;
    }
  }
  
  // 针对特殊情况的映射
  const specialMappings: Record<string, string> = {
    'casual': 'hypercasual-games',
    'card': 'bejeweled-games',
    'board': 'strategy-games',
  };
  
  for (const [key, value] of Object.entries(specialMappings)) {
    if (name.includes(key)) {
      return `/images/cat/${value}.jpg`;
    }
  }
  
  // 默认使用action-games图片
  return '/images/cat/action-games.jpg';
}

// 获取分类描述
function getCategoryDescription(categoryName: string): string {
  const name = categoryName.toLowerCase();
  if (name.includes('action')) return 'Fast-paced adventure games requiring quick reflexes and precision';
  if (name.includes('puzzle')) return 'Games that test your intelligence and problem-solving abilities';
  if (name.includes('racing')) return 'Race with various vehicles on different tracks';
  if (name.includes('multiplayer')) return 'Games to play with friends or players around the world';
  if (name.includes('shooting')) return 'Aim, shoot, and hit targets in these exciting games';
  if (name.includes('arcade')) return 'Classic arcade-style games with simple mechanics and addictive gameplay';
  if (name.includes('io')) return 'Simple but addictive multiplayer online games';
  if (name.includes('casual')) return 'Easy to play games for players of all ages';
  if (name.includes('strategy')) return 'Plan your moves and outsmart your opponents';
  if (name.includes('sports')) return 'Compete in various sports competitions';
  if (name.includes('adventure')) return 'Explore exciting worlds and complete quests';
  if (name.includes('fighting')) return 'Test your combat skills against opponents';
  if (name.includes('cooking')) return 'Prepare delicious meals and manage restaurants';
  if (name.includes('girls')) return 'Fun games designed with girls in mind';
  if (name.includes('boys')) return 'Action-packed games for boys of all ages';
  return 'Exciting games for everyone to enjoy'; // 默认描述
}

// 将category name转换为URL友好的格式
function getCategorySlug(name: string): string {
  return name.toLowerCase().replace(/\s+/g, '-');
}

async function getCategories() {
  try {
    const apiBaseUrl = 'https://api.planetclicker.pro';
    const response = await fetch(`${apiBaseUrl}/categories`, { next: { revalidate: 3600 } });
    
    if (!response.ok) {
      console.error(`Failed to fetch categories: ${response.status}`);
      return [];
    }
    
    const result: CategoriesApiResponse = await response.json();
    
    if (result.status === 'success' && Array.isArray(result.data)) {
      return result.data;
    } else {
      console.error(`Failed to parse categories:`, result);
      return [];
    }
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// 获取每个分类的前4个游戏
async function getCategoryGames(categoryName: string) {
  try {
    const apiBaseUrl = 'https://api.planetclicker.pro';
    const encodedName = encodeURIComponent(categoryName);
    const response = await fetch(`${apiBaseUrl}/games/category/name/${encodedName}?limit=4`, { next: { revalidate: 3600 } });
    
    if (!response.ok) {
      console.error(`Failed to fetch games for category ${categoryName}: ${response.status}`);
      return [];
    }
    
    const result: GamesApiResponse = await response.json();
    
    if (result.status === 'success' && Array.isArray(result.data)) {
      return result.data.map(game => ({
        id: game.game_id.toString(),
        title: game.name,
        slug: game.game_name,
        image: game.image
      }));
    } else {
      console.error(`Failed to parse games for category ${categoryName}:`, result);
      return [];
    }
  } catch (error) {
    console.error(`Error fetching games for category ${categoryName}:`, error);
    return [];
  }
}

export default async function CategoriesPage() {
  // 获取所有分类
  const categories = await getCategories();
  
  // 处理每个分类，获取游戏
  const categoriesWithGames = await Promise.all(
    categories.map(async (category) => {
      const games = await getCategoryGames(category.name);
      return {
        id: getCategorySlug(category.name),
        name: category.name,
        image: getCategoryImage(category.name),
        description: getCategoryDescription(category.name),
        games: games
      };
    })
  );
  
  return (
    <Layout>
      <div className="max-w-screen-xl mx-auto px-4 py-8">
        <h1 className="text-2xl md:text-3xl font-bold mb-6">Game Categories</h1>
        
        {categoriesWithGames.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categoriesWithGames.map((category) => (
              <Link 
                href={`/category/${category.id}`} 
                key={category.id}
                className="bg-card hover:bg-card/80 rounded-lg overflow-hidden transition-all duration-200"
              >
                <div className="aspect-video relative">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent">
                    <div className="absolute bottom-0 left-0 p-4">
                      <h2 className="text-xl font-bold text-white">{category.name}</h2>
                      <p className="text-sm text-gray-300 mt-1">{category.description}</p>
                    </div>
                  </div>
                </div>
                
                <div className="p-4">
                  {category.games && category.games.length > 0 ? (
                    <>
                      <div className="grid grid-cols-2 gap-2">
                        {category.games.slice(0, 4).map((game) => (
                          <div key={game.id} className="relative aspect-video rounded overflow-hidden">
                            <Image
                              src={game.image}
                              alt={game.title}
                              fill
                              className="object-cover"
                              sizes="(max-width: 768px) 50vw, 25vw"
                            />
                          </div>
                        ))}
                      </div>
                      <div className="mt-3 text-center">
                        <span className="text-primary hover:underline">View all games</span>
                      </div>
                    </>
                  ) : (
                    <div className="h-28"></div>
                  )}
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-card rounded-lg">
            <Gamepad2 className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h2 className="text-xl font-bold mb-2">No Categories Found</h2>
            <p className="text-muted-foreground mb-6">
              We couldn't load the game categories. Please try again later.
            </p>
          </div>
        )}
      </div>
    </Layout>
  );
} 