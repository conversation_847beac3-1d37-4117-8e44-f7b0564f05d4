---
description:
globs:
alwaysApply: false
---
# Tailwind CSS指南

## 颜色系统

项目使用以下主题颜色：

```
// 主要颜色
bg-primary: 主背景色
text-primary: 主文本色
accent-primary: 强调色

// 辅助颜色
bg-secondary: 次要背景色
text-secondary: 次要文本色
accent-secondary: 次要强调色
```

## 响应式设计

项目使用以下断点进行响应式设计：

```
sm: 640px   // 小屏幕手机
md: 768px   // 大屏幕手机/小平板
lg: 1024px  // 平板/小笔记本
xl: 1280px  // 笔记本
2xl: 1536px // 桌面显示器
```

使用示例：

```tsx
<div className="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5">
  {/* 内容 */}
</div>
```

## 间距系统

项目使用以下间距系统：

```
p-{size}: 内边距
m-{size}: 外边距
gap-{size}: 网格间距

// 尺寸从0到96，常用的有：
// 0, 1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24, 32, 40, 48, 56, 64
```

## 布局组件

### 容器

```tsx
<div className="container mx-auto px-4">
  {/* 内容 */}
</div>
```

### 卡片

```tsx
<div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
  {/* 卡片内容 */}
</div>
```

### 网格布局

```tsx
<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
  {/* 网格项 */}
</div>
```

### Flex布局

```tsx
<div className="flex flex-col md:flex-row items-center justify-between gap-4">
  {/* Flex项 */}
</div>
```

## 动画

使用Tailwind的动画类：

```
hover:scale-105 transition-transform: 悬停时放大
animate-pulse: 脉冲动画
animate-spin: 旋转动画
animate-bounce: 弹跳动画
```

## 深色模式

项目支持深色模式，使用dark:前缀应用深色模式样式：

```tsx
<div className="bg-white text-gray-900 dark:bg-gray-800 dark:text-gray-100">
  {/* 内容 */}
</div>
```
