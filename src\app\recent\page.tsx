"use client";

import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import GameCard from '@/components/game/GameCard';
import Link from 'next/link';
import { Clock, History, X, Trash2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

// 最近玩过的游戏接口
interface RecentGame {
  id: string;
  title: string;
  slug: string;
  image: string;
  lastPlayedAt: string;
}

export default function RecentGamesPage() {
  const [recentGames, setRecentGames] = useState<RecentGame[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 加载最近玩过的游戏
  useEffect(() => {
    const loadRecentGames = () => {
      try {
        const storedGames = localStorage.getItem('recentGames');
        if (storedGames) {
          setRecentGames(JSON.parse(storedGames));
        }
      } catch (error) {
        console.error('Error loading recent games:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadRecentGames();

    // 监听storage事件，当其他页面更新游戏记录时刷新
    window.addEventListener('storage', loadRecentGames);
    
    return () => {
      window.removeEventListener('storage', loadRecentGames);
    };
  }, []);

  // 清除单个游戏的历史记录
  const removeGame = (gameId: string) => {
    try {
      const updatedGames = recentGames.filter(game => game.id !== gameId);
      setRecentGames(updatedGames);
      localStorage.setItem('recentGames', JSON.stringify(updatedGames));
      window.dispatchEvent(new Event('storage'));
    } catch (error) {
      console.error('Error removing game from history:', error);
    }
  };

  // 清除所有游戏历史记录
  const clearAllHistory = () => {
    if (confirm('Are you sure you want to clear your entire play history?')) {
      try {
        setRecentGames([]);
        localStorage.removeItem('recentGames');
        window.dispatchEvent(new Event('storage'));
      } catch (error) {
        console.error('Error clearing game history:', error);
      }
    }
  };

  // 格式化最后玩游戏的时间
  const formatLastPlayed = (dateString: string): string => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return 'recently';
    }
  };

  return (
    <Layout>
      <div className="max-w-screen-xl mx-auto px-4 py-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4 sm:mb-6">
          <div className="flex items-center">
            <History className="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold">Recently Played</h1>
          </div>
          
          {recentGames.length > 0 && (
            <button 
              onClick={clearAllHistory}
              className="self-end sm:self-auto text-xs sm:text-sm text-muted-foreground hover:text-white flex items-center gap-1 px-2 py-1 rounded hover:bg-card/30 transition-colors"
            >
              <Trash2 size={14} className="sm:mr-1" />
              <span>Clear History</span>
            </button>
          )}
        </div>

        {isLoading ? (
          <div className="text-center py-8 sm:py-12">
            <div className="animate-pulse flex justify-center">
              <Clock className="h-8 w-8 text-muted-foreground" />
            </div>
            <p className="mt-4">Loading your game history...</p>
          </div>
        ) : recentGames.length > 0 ? (
          <div className="space-y-4 sm:space-y-8">
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-6">
              {recentGames.map(game => (
                <div key={game.id} className="relative group">
                  <GameCard
                    id={game.id}
                    title={game.title}
                    slug={game.slug}
                    image={game.image}
                  />
                  <div className="absolute top-1 right-1 sm:top-2 sm:right-2">
                    <button 
                      onClick={() => removeGame(game.id)}
                      className="bg-black/70 hover:bg-black p-1.5 sm:p-2 rounded-full opacity-80 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity"
                      title="Remove from history"
                      aria-label="Remove from history"
                    >
                      <X className="w-4 h-4 sm:w-5 sm:h-5 text-red-400" />
                    </button>
                  </div>
                  
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8 sm:py-12 bg-card rounded-lg">
            <Clock className="mx-auto w-10 h-10 sm:w-12 sm:h-12 text-muted-foreground mb-4" />
            <h2 className="text-lg sm:text-xl font-bold mb-2">No Play History Yet</h2>
            <p className="text-muted-foreground mb-6 px-4 sm:px-6 max-w-md mx-auto text-sm sm:text-base">
              You haven't played any games yet. Start playing to track your game history!
            </p>
            <Link
              href="/"
              className="bg-primary hover:bg-primary/90 text-white rounded px-5 py-2 sm:px-6 sm:py-3 inline-block text-sm sm:text-base font-medium"
            >
              Find Games to Play
            </Link>
          </div>
        )}
      </div>
    </Layout>
  );
} 