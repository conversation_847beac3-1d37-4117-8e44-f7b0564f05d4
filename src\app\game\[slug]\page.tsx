import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import GameDetailsClient from './GameDetailsClient';
import GameSection from '@/components/game/GameSection';
import { GameCardProps } from '@/components/game/GameCard';
import { gameApi, ApiGame } from '@/lib/api';

// 将API游戏数据转换为GameCardProps格式
function mapApiGameToGameCard(apiGame: ApiGame): GameCardProps {
  return {
    id: apiGame.game_id.toString(),
    title: apiGame.name,
    slug: apiGame.game_name,
    image: apiGame.image,
    // 可选字段
    badge: apiGame.featured === '1' ? 'hot' : undefined
  };
}

// 为页面生成元数据
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  // 正确处理动态参数：先等待整个params对象
  const resolvedParams = await params;
  const slug = resolvedParams.slug;
  
  try {
    const response = await gameApi.getGameDetail(slug);
    const gameDetails = response.data;

    if (!gameDetails) {
    return {
        title: 'Game not found - Planet Clicker',
        description: 'The game you are looking for was not found on Planet Clicker.',
    };
  }

  return {
      title: `Play ${gameDetails.name} - Planet Clicker`,
      description: gameDetails.description || `Play ${gameDetails.name} for free on Planet Clicker. ${gameDetails.name} is one of our best games!`,
    openGraph: {
        title: `Play ${gameDetails.name} - Planet Clicker`,
        description: gameDetails.description || `Play ${gameDetails.name} for free on Planet Clicker. ${gameDetails.name} is one of our best games!`,
        images: [gameDetails.image],
    },
  };
  } catch (error) {
    console.error('获取游戏详情元数据时出错:', error);
    return {
      title: 'Game not found - Planet Clicker',
      description: 'The game you are looking for was not found on Planet Clicker.',
    };
  }
}

// 游戏详情页组件
export default async function GameDetailsPage({ params }: { params: { slug: string } }) {
  // 正确处理动态参数：先等待整个params对象
  const resolvedParams = await params;
  const slug = resolvedParams.slug;
  
  try {
    // 获取游戏详情
    const gameResponse = await gameApi.getGameDetail(slug);
    const gameDetails = gameResponse.data;
    
    // 如果没有找到游戏，则返回404
    if (!gameDetails) {
      notFound();
    }
    
    // 获取相关游戏 - 优先通过分类获取，如果分类为空或获取失败则尝试通过标签获取
    let relatedApiGames: ApiGame[] = [];
    
    if (gameDetails.category_name) {
      console.log(`尝试通过分类获取相关游戏: ${gameDetails.category_name}`);
      const categoryResponse = await gameApi.getRelatedGamesByCategory(gameDetails.category_name, gameDetails.game_id);
      relatedApiGames = categoryResponse.data.filter(game => game.game_id !== gameDetails.game_id);
      console.log(`分类${gameDetails.category_name}获取到的相关游戏数量: ${relatedApiGames.length}`);
    }
    
    // 如果通过分类获取的相关游戏不足，并且游戏有标签，则尝试通过第一个标签获取
    if (relatedApiGames.length < 10 && gameDetails.tags && gameDetails.tags.length > 0) {
      console.log(`尝试通过标签获取相关游戏: ${gameDetails.tags[0].name}`);
      const tagResponse = await gameApi.getRelatedGamesByTag(gameDetails.tags[0].url, gameDetails.game_id);
      const tagGames = tagResponse.data.filter(game => game.game_id !== gameDetails.game_id);
      console.log(`标签${gameDetails.tags[0].name}获取到的相关游戏数量: ${tagGames.length}`);
      
      // 合并游戏列表，并确保没有重复
      const existingIds = new Set(relatedApiGames.map(game => game.game_id));
      for (const game of tagGames) {
        if (!existingIds.has(game.game_id)) {
          relatedApiGames.push(game);
          existingIds.add(game.game_id);
          
          // 如果已经有足够的相关游戏，就停止添加
          if (relatedApiGames.length >= 10) break;
        }
      }
    }
    
    // 将API游戏数据转换为GameCardProps格式
    const relatedGames = relatedApiGames.map(mapApiGameToGameCard);
    console.log(`最终获取到的相关游戏数量: ${relatedGames.length}`);
    
    // 将相关游戏传递给GameDetailsClient组件
    return <GameDetailsClient gameDetails={gameDetails} relatedGames={relatedGames} />;
  } catch (error) {
    console.error('获取游戏详情页数据时出错:', error);
    notFound();
  }
}
