---
description: 
globs: 
alwaysApply: false
---
# 游戏数据模型

## 游戏对象

游戏对象是网站的核心数据结构，代表一个可玩的游戏。

```typescript
interface Game {
  game_id: number;        // 游戏ID
  catalog_id: string;     // 目录ID
  game_name: string;      // 游戏URL名称（用于SEO友好的URL）
  name: string;           // 游戏显示名称
  image: string;          // 游戏缩略图URL
  plays: number;          // 游戏被玩的次数
  rating: string;         // 游戏评分
  description: string;    // 游戏描述
  instructions: string;   // 游戏指令
  file: string;           // 游戏文件
  game_type: string;      // 游戏类型，如HTML5
  w: number;              // 宽度
  h: number;              // 高度
  date_added: string;     // 添加日期
  published: string;      // 是否已发布
  featured: string;       // 是否推荐
  mobile: string;         // 是否适配移动端
  video_url: string;      // 游戏视频URL
  category_name: string;  // 分类名称
  tags: Tag[];            // 标签数组
  like_count: number;     // 点赞数
  favorite_count: number; // 收藏数
}
```

## 标签对象

标签用于对游戏进行分类。

```typescript
interface Tag {
  id: number;  // 标签ID
  name: string; // 标签名称
  url: string;  // 标签URL
}
```

## 分类对象

分类是游戏的主要组织方式。

```typescript
interface Category {
  id: number;        // 分类ID
  name: string;      // 分类名称
  url: string;       // 分类URL
  img: string;       // 分类图片URL
  description: string; // 分类描述
  game_count: number;  // 该分类下的游戏数量
}
```

## 搜索请求

搜索游戏时使用的请求结构。

```typescript
interface SearchRequest {
  keyword: string;     // 搜索关键词（必填）
  category_id?: number; // 分类ID（可选）
  tag_id?: number;     // 标签ID（可选）
  min_rating?: number; // 最低评分（可选）
  max_rating?: number; // 最高评分（可选）
  skip?: number;       // 跳过的数量（默认值：0）
  limit?: number;      // 返回的数量（默认值：10）
}
```

