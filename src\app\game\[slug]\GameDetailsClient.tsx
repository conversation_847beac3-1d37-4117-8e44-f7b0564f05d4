"use client";

import { useState, useRef, useEffect, useMemo } from 'react';
import Image from 'next/image';
import Layout from '@/components/layout/Layout';
import Link from 'next/link';
import { Heart } from 'lucide-react';
import { Toast } from '@/components/ui/toast';
import Markdown from '@/components/ui/Markdown';
import GameSection from '@/components/game/GameSection';
import { GameCardProps } from '@/components/game/GameCard';
import GamePageAds from '@/components/ads/GamePageAds';
import GameContainer from '@/components/game/GameContainer';

// 游戏的API数据结构
interface ApiGameDetail {
  game_id: number;
  catalog_id?: string;
  game_name: string; // slug
  name: string;      // title
  image: string;
  plays?: number;
  rating?: string;
  description?: string;
  instructions?: string;
  file?: string;
  game_type?: string;
  w?: number;
  h?: number;
  date_added?: string;
  published?: string;
  featured?: string;
  mobile?: string;
  video_url?: string;
  category_name?: string;
  tags?: {
    id: number;
    name: string;
    url: string;
  }[];
  like_count?: number;
  favorite_count?: number;
}

// 创建替代的评分和评论组件，因为找不到原始组件
const GameRating = ({ rating, totalRatings, gameTitle }: { rating: string; totalRatings: number; gameTitle: string }) => {
  const [userRating, setUserRating] = useState<number>(0);
  const [hoveredStar, setHoveredStar] = useState<number>(0);
  const [hasRated, setHasRated] = useState<boolean>(false);
  const [ratingMessage, setRatingMessage] = useState<string>('');
  
  // 在组件加载时，检查用户是否已经为这个游戏评分
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const userRatings = JSON.parse(localStorage.getItem('gameRatings') || '{}');
        if (userRatings[gameTitle]) {
          setUserRating(userRatings[gameTitle]);
          setHasRated(true);
        }
      } catch (error) {
        console.error('Error reading ratings from localStorage:', error);
      }
    }
  }, [gameTitle]);
  
  // 处理用户评分
  const handleRating = (star: number) => {
    setUserRating(star);
    setHasRated(true);
    
    // 保存评分到 localStorage
    try {
      const userRatings = JSON.parse(localStorage.getItem('gameRatings') || '{}');
      userRatings[gameTitle] = star;
      localStorage.setItem('gameRatings', JSON.stringify(userRatings));
      
      // 显示成功消息
      setRatingMessage('Thank you for rating this game!');
      setTimeout(() => setRatingMessage(''), 3000);
    } catch (error) {
      console.error('Error saving rating to localStorage:', error);
    }
  };
  
  return (
    <div className="border border-gray-600 bg-background rounded-lg p-4 mb-4">
      <h3 className="font-bold mb-3">Rating</h3>
      <div className="flex items-center gap-2 mb-3">
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <svg 
              key={i} 
              className={`w-6 h-6 ${i < parseInt(rating) ? 'text-yellow-400' : 'text-gray-400'}`} 
              fill="currentColor" 
              viewBox="0 0 20 20" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ))}
        </div>
        <span className="text-xl font-bold">{rating}</span>
        <span className="text-sm text-muted-foreground">({totalRatings.toLocaleString()})</span>
      </div>
      <div className="border-t border-gray-600 pt-4">
        <h4 className="font-medium mb-2">Rate {gameTitle}</h4>
        <div className="flex gap-1 mb-2">
          {[1, 2, 3, 4, 5].map((star) => (
            <button 
              key={star} 
              onClick={() => !hasRated && handleRating(star)} 
              onMouseEnter={() => !hasRated && setHoveredStar(star)}
              onMouseLeave={() => !hasRated && setHoveredStar(0)}
              className={`focus:outline-none transition-colors ${hasRated ? 'cursor-default' : 'cursor-pointer'}`}
              disabled={hasRated}
              aria-label={`Rate ${star} star${star !== 1 ? 's' : ''}`}
            >
              <svg 
                className={`w-7 h-7 ${
                  hasRated && userRating >= star ? 'text-yellow-400' : 
                  !hasRated && hoveredStar >= star ? 'text-yellow-400' : 'text-gray-400'
                } ${!hasRated && 'hover:text-yellow-400'}`} 
                fill="currentColor" 
                viewBox="0 0 20 20" 
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </button>
          ))}
        </div>
        {ratingMessage ? (
          <p className="text-sm text-green-500 mt-2">
            {ratingMessage}
          </p>
        ) : hasRated ? (
          <p className="text-sm text-green-500 mt-2">
            You rated this game {userRating} star{userRating !== 1 ? 's' : ''}
          </p>
        ) : (
          <p className="text-xs text-muted-foreground mt-2">
            Click on a star to rate this game
          </p>
        )}
      </div>
    </div>
  );
};

// 收藏的游戏结构
interface FavoriteGame {
  id: string;
  title: string;
  slug: string;
  image: string;
  addedAt: string;
}

// 最近玩过的游戏结构
interface RecentGame {
  id: string;
  title: string;
  slug: string;
  image: string;
  lastPlayedAt: string;
}

// 从localStorage读取收藏游戏列表
function getFavoriteGames(): FavoriteGame[] {
  if (typeof window === 'undefined') return [];
  
  try {
    const favorites = localStorage.getItem('favoriteGames');
    return favorites ? JSON.parse(favorites) : [];
  } catch (error) {
    console.error('Error reading favorites from localStorage:', error);
    return [];
  }
}

// 保存游戏到收藏列表
function saveFavorite(game: FavoriteGame): void {
  try {
    const favorites = getFavoriteGames();
    
    // 检查是否已经收藏
    const existingIndex = favorites.findIndex(fav => fav.id === game.id);
    
    if (existingIndex >= 0) {
      // 如果已经收藏，则移除
      favorites.splice(existingIndex, 1);
    } else {
      // 如果未收藏，则添加
      favorites.push(game);
    }
    
    localStorage.setItem('favoriteGames', JSON.stringify(favorites));
  } catch (error) {
    console.error('Error saving favorite to localStorage:', error);
  }
}

// 检查游戏是否已收藏
function isFavorite(gameId: string): boolean {
  const favorites = getFavoriteGames();
  return favorites.some(fav => fav.id === gameId);
}

// 从localStorage读取最近玩过的游戏列表
function getRecentGames(): RecentGame[] {
  if (typeof window === 'undefined') return [];
  
  try {
    const recentGames = localStorage.getItem('recentGames');
    return recentGames ? JSON.parse(recentGames) : [];
  } catch (error) {
    console.error('Error reading recent games from localStorage:', error);
    return [];
  }
}

// 添加游戏到最近玩过列表
function addToRecentGames(game: RecentGame): void {
  try {
    const recentGames = getRecentGames();
    
    // 检查是否已经在列表中
    const existingIndex = recentGames.findIndex(g => g.id === game.id);
    
    if (existingIndex >= 0) {
      // 如果已经存在，则移除旧记录
      recentGames.splice(existingIndex, 1);
    }
    
    // 添加到列表最前面（最新玩的）
    recentGames.unshift(game);
    
    // 如果记录超过20个，只保留最新的20个
    const trimmedList = recentGames.slice(0, 20);
    
    localStorage.setItem('recentGames', JSON.stringify(trimmedList));
    
    // 触发storage事件，以便其他页面同步更新
    window.dispatchEvent(new Event('storage'));
  } catch (error) {
    console.error('Error saving recent game to localStorage:', error);
  }
}

// 游戏详情页面客户端组件
export default function GameDetailsClient({ 
  gameDetails, 
  relatedGames = [] 
}: { 
  gameDetails: ApiGameDetail; 
  relatedGames?: GameCardProps[]; 
}) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const gameContainerRef = useRef<import('@/components/game/GameContainer').GameContainerRef>(null);

  // 在组件挂载时检查游戏是否已被收藏
  useEffect(() => {
    setIsFavorited(isFavorite(gameDetails.game_id.toString()));
  }, [gameDetails.game_id]);

  // 解析发布日期，添加日期验证以避免显示1970年
  const releaseDate = useMemo(() => {
    // 记录原始日期值用于调试
    console.log('Original date_added value:', gameDetails.date_added);
    
    if (!gameDetails.date_added) {
      return 'January 2023'; // 默认日期
    }
    
    try {
      const dateObj = new Date(gameDetails.date_added);
      
      // 检查日期是否有效（不是1970年且是一个有效日期）
      if (dateObj.getFullYear() <= 1970 || isNaN(dateObj.getTime())) {
        console.log('Invalid date detected:', gameDetails.date_added);
        return 'January 2023'; // 默认日期
      }
      
      return dateObj.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
    } catch (error) {
      console.error('Error parsing date:', error);
      return 'January 2023'; // 出错时返回默认日期
    }
  }, [gameDetails.date_added]);

  // 游戏URL
  const gameUrl = gameDetails.file || '';

  // 开始游戏
  const startPlaying = () => {
    setIsPlaying(true);
    
    // 添加到最近玩过的游戏列表
    const recentGame: RecentGame = {
      id: gameDetails.game_id.toString(),
      title: gameDetails.name,
      slug: gameDetails.game_name,
      image: gameDetails.image,
      lastPlayedAt: new Date().toISOString()
    };
    
    addToRecentGames(recentGame);
  };

  // 打开全屏
  const handleFullscreen = () => {
    if (isPlaying && gameContainerRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen().then(() => setIsFullscreen(false));
      } else {
        gameContainerRef.current.requestFullscreen().then(() => setIsFullscreen(true));
      }
    } else {
      // 如果还没开始玩，先开始游戏再全屏
      startPlaying();
      setTimeout(() => {
        if (gameContainerRef.current) {
          gameContainerRef.current.requestFullscreen().then(() => setIsFullscreen(true));
        }
      }, 500);
    }
  };

  // 切换收藏状态
  const toggleFavorite = () => {
    // 创建要保存的游戏对象
    const favoriteGame: FavoriteGame = {
      id: gameDetails.game_id.toString(),
      title: gameDetails.name,
      slug: gameDetails.game_name,
      image: gameDetails.image,
      addedAt: new Date().toISOString()
    };
    
    // 保存到localStorage
    saveFavorite(favoriteGame);
    
    // 更新UI状态
    const newFavoritedState = !isFavorited;
    setIsFavorited(newFavoritedState);
    
    // 显示Toast通知
    setToastMessage(newFavoritedState 
      ? `Added ${gameDetails.name} to favorites!` 
      : `Removed ${gameDetails.name} from favorites!`
    );
    setShowToast(true);
  };

  // 使用API获取的评分，或者如果API没有提供，则使用默认值
  const rating = gameDetails.rating || '4.5';
  const totalRatings = gameDetails.plays || 500;

  return (
    <Layout showControllerBanner={false}>
      <h1 className="text-2xl md:text-3xl font-bold text-white max-w-screen-xl mx-auto px-4">{gameDetails.name}</h1>
      
      <GamePageAds>
        <div className="border border-gray-600 bg-background rounded-lg overflow-hidden mb-8">
          <div className="aspect-video relative">
            {isPlaying ? (
              <GameContainer 
                ref={gameContainerRef}
                src={gameUrl}
                isPlaying={isPlaying}
                gameType={gameDetails.game_type || 'html5'}
                width={gameDetails.w || undefined}
                height={gameDetails.h || undefined}
              />
            ) : (
              <>
                <Image
                  src={gameDetails.image}
                  alt={gameDetails.name}
                  fill
                  className="object-cover"
                  priority
                />
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                  <button 
                    onClick={startPlaying}
                    className="bg-primary hover:bg-primary/90 text-white rounded-full w-20 h-20 flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z" />
                    </svg>
                  </button>
                </div>
              </>
            )}
          </div>

          <div className="p-4">
            <div className="flex flex-wrap justify-between items-center mb-4 gap-2">
              <div className="flex gap-2">
                <button 
                  onClick={startPlaying}
                  className="bg-primary hover:bg-primary/90 text-white rounded px-3 sm:px-4 py-2 font-medium flex items-center"
                  aria-label="Play Now"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1.5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8 5v14l11-7z" />
                  </svg>
                  <span>Play Now</span>
                </button>
                <button 
                  onClick={handleFullscreen}
                  className="bg-secondary hover:bg-secondary/90 text-white rounded px-2 sm:px-4 py-2 font-medium flex items-center"
                  aria-label="Fullscreen"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:mr-1.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" />
                  </svg>
                  <span className="hidden sm:inline">Fullscreen</span>
                </button>
              </div>

              <button 
                onClick={toggleFavorite}
                className="flex items-center gap-1 px-2 py-1.5 rounded hover:bg-card/80 text-muted-foreground hover:text-white transition-colors"
                aria-label={isFavorited ? "Remove from Favorites" : "Add to Favorites"}
              >
                <Heart 
                  size={20} 
                  className={isFavorited ? "text-red-500 fill-red-500" : ""} 
                />
                <span className="hidden sm:inline">{isFavorited ? "Remove from Favorites" : "Add to Favorites"}</span>
              </button>
            </div>

            <div className="prose prose-sm prose-invert max-w-none">
              <p>
                Enjoy playing {gameDetails.name} on Planet Clicker directly in your browser.
                This game has received positive reviews from our users. If you like this game,
                check out our other exciting games.
              </p>
            </div>
          </div>
        </div>
      </GamePageAds>
      
      {/* 游戏详情和描述部分 - 与上方保持相同的总宽度 */}
      <div className="max-w-screen-xl mx-auto px-4 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            {/* Game details and description */}
            <div className="border border-gray-600 bg-background rounded-lg p-6">
              <h2 className="text-xl font-bold mb-4">About {gameDetails.name}</h2>
              {gameDetails.description ? (
                gameDetails.description.includes('<') ? (
                  <div 
                    className="text-muted-foreground prose prose-invert max-w-none"
                    dangerouslySetInnerHTML={{ __html: gameDetails.description }}
                  />
                ) : (
                  <p className="text-muted-foreground">{gameDetails.description}</p>
                )
              ) : (
                <p className="text-muted-foreground">
                  {`${gameDetails.name} is an exciting game that you can play for free. 
                  Immerse yourself in an amazing gameplay experience with stunning graphics and engaging mechanics.
                  This game is perfect for players of all ages who enjoy popular games.`}
                </p>
              )}
              
              <div className="mt-6 grid grid-cols-2 gap-4">
                <div className="border-t border-gray-600 pt-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Platform</h3>
                  <p>Browser {gameDetails.mobile === '1' ? '(Desktop, Mobile)' : '(Desktop)'}</p>
                </div>
                <div className="border-t border-gray-600 pt-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Genre</h3>
                  <p>{gameDetails.category_name || 'Casual'}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            {/* Rating widget */}
            <GameRating rating={rating} totalRatings={totalRatings} gameTitle={gameDetails.name} />
            
            {/* Game info card */}
            <div className="border border-gray-600 bg-background rounded-lg p-4 mb-4">
              <h3 className="font-bold mb-2">How to play</h3>
              <div className="border-t border-gray-600 pt-4 mt-2">
                {gameDetails.instructions ? (
                  gameDetails.instructions.includes('<') ? (
                    <div 
                      className="text-sm text-muted-foreground mb-4 prose prose-invert max-w-none prose-sm"
                      dangerouslySetInnerHTML={{ __html: gameDetails.instructions }}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground mb-4">{gameDetails.instructions}</p>
                  )
                ) : (
                  <p className="text-sm text-muted-foreground mb-4">
                    Use your keyboard or mouse to control the game. Click the play button to start immediately.
                  </p>
                )}
                
                {/* 只有在没有instructions的情况下才显示通用控制提示 */}
                {!gameDetails.instructions && (
                  <div className="mb-4">
                    <p className="text-sm text-muted-foreground">
                      Most browser games use the following common controls:
                    </p>
                    <ul className="mt-2 text-sm text-muted-foreground list-disc pl-5 space-y-1">
                      <li>Use mouse for point-and-click games</li>
                      <li>Arrow keys or WASD for movement</li>
                      <li>Space bar for jumping or primary action</li>
                      <li>ESC key to pause or access menu</li>
                    </ul>
                    <p className="mt-2 text-xs text-muted-foreground italic">
                      Note: Controls may vary by game. Check in-game tutorials for specific instructions.
                    </p>
                  </div>
                )}
              </div>
              
              {gameDetails.tags && gameDetails.tags.length > 0 && (
                <div className="mt-4 border-t border-gray-600 pt-4">
                  <h3 className="font-bold mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {gameDetails.tags.map(tag => (
                      <Link 
                        key={tag.id} 
                        href={`/tag/${tag.url}`}
                        className="bg-background border border-gray-600 hover:bg-gray-800 px-2 py-1 text-xs rounded transition-colors"
                      >
                        {tag.name}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* 相关游戏部分 - 在页面底部显示 */}
      {relatedGames.length > 0 && (
        <div className="max-w-screen-xl mx-auto mb-12 px-4">
          <div className="border border-gray-600 bg-background rounded-lg p-6">
            <GameSection 
              title="Similar Games You Might Like" 
              games={relatedGames} 
              viewMoreLink={gameDetails.category_name ? `/category/${gameDetails.category_name.toLowerCase().replace(/\s+/g, '-')}` : '/games'}
              className="similar-games-section"
              compact={true}
            />
          </div>
        </div>
      )}

      {/* 使用新的Toast组件 */}
      <Toast
        message={toastMessage}
        visible={showToast}
        variant="success"
        position="bottom-right"
        duration={3000}
        onClose={() => setShowToast(false)}
      />
    </Layout>
  );
} 