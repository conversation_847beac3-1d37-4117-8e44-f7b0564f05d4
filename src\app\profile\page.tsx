"use client";

import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import Image from 'next/image';
import Link from 'next/link';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { featuredGames, newGames } from '@/lib/gameData';
import GameCard from '@/components/game/GameCard';
import { Edit, LogOut, Bell, Settings, User, Clock } from 'lucide-react';

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('favorites');
  
  // For demo purposes - in a real app, these would come from an API
  const userFavorites = featuredGames.slice(0, 6);
  const recentlyPlayed = newGames.slice(0, 6);
  
  return (
    <Layout>
      <div className="max-w-screen-xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-start gap-6 mb-8">
          {/* User profile sidebar */}
          <div className="w-full md:w-72 bg-card rounded-lg p-6">
            <div className="flex flex-col items-center text-center mb-6">
              <div className="relative mb-4">
                <div className="w-24 h-24 rounded-full overflow-hidden">
                  <Image
                    src="https://i.pravatar.cc/300?img=8"
                    alt="User Avatar"
                    width={96}
                    height={96}
                    className="object-cover"
                  />
                </div>
                <button className="absolute bottom-0 right-0 bg-primary hover:bg-primary/90 rounded-full p-1.5">
                  <Edit size={14} className="text-white" />
                </button>
              </div>
              
              <h1 className="text-xl font-bold">GamerUser123</h1>
              <p className="text-muted-foreground text-sm">Member since Jan 2023</p>
              
              <div className="flex gap-2 mt-4">
                <button className="bg-primary hover:bg-primary/90 text-white text-sm rounded px-4 py-2 flex items-center gap-1">
                  <Edit size={14} />
                  <span>Edit Profile</span>
                </button>
                <button className="bg-secondary hover:bg-secondary/90 text-white text-sm rounded px-3 py-2">
                  <LogOut size={16} />
                </button>
              </div>
            </div>
            
            <div className="border-t border-border pt-4">
              <h3 className="font-medium mb-3">Stats</h3>
              <div className="grid grid-cols-2 gap-2 text-center">
                <div className="bg-background rounded p-2">
                  <div className="text-lg font-bold">42</div>
                  <div className="text-xs text-muted-foreground">Games Played</div>
                </div>
                <div className="bg-background rounded p-2">
                  <div className="text-lg font-bold">18</div>
                  <div className="text-xs text-muted-foreground">Favorites</div>
                </div>
                <div className="bg-background rounded p-2">
                  <div className="text-lg font-bold">5</div>
                  <div className="text-xs text-muted-foreground">Reviews</div>
                </div>
                <div className="bg-background rounded p-2">
                  <div className="text-lg font-bold">3.2h</div>
                  <div className="text-xs text-muted-foreground">Avg. Time</div>
                </div>
              </div>
            </div>
            
            <div className="border-t border-border pt-4 mt-4">
              <nav className="space-y-1">
                <Link href="/profile" className="flex items-center gap-2 py-2 px-3 bg-background rounded text-sm">
                  <User size={16} />
                  <span>Profile</span>
                </Link>
                <Link href="/profile/notifications" className="flex items-center gap-2 py-2 px-3 hover:bg-background rounded text-sm text-muted-foreground hover:text-white">
                  <Bell size={16} />
                  <span>Notifications</span>
                </Link>
                <Link href="/profile/history" className="flex items-center gap-2 py-2 px-3 hover:bg-background rounded text-sm text-muted-foreground hover:text-white">
                  <Clock size={16} />
                  <span>Play History</span>
                </Link>
                <Link href="/profile/settings" className="flex items-center gap-2 py-2 px-3 hover:bg-background rounded text-sm text-muted-foreground hover:text-white">
                  <Settings size={16} />
                  <span>Settings</span>
                </Link>
              </nav>
            </div>
          </div>
          
          {/* Main content */}
          <div className="flex-1">
            <h2 className="text-2xl font-bold mb-6">My Games</h2>
            
            <Tabs defaultValue="favorites" className="w-full">
              <TabsList className="mb-6">
                <TabsTrigger 
                  value="favorites"
                  className="data-[state=active]:bg-primary"
                  onClick={() => setActiveTab('favorites')}
                >
                  Favorites
                </TabsTrigger>
                <TabsTrigger 
                  value="recently-played"
                  className="data-[state=active]:bg-primary"
                  onClick={() => setActiveTab('recently-played')}
                >
                  Recently Played
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="favorites">
                {userFavorites.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    {userFavorites.map((game) => (
                      <GameCard key={game.id} {...game} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12 bg-card rounded-lg">
                    <h3 className="font-medium mb-2">No favorites yet</h3>
                    <p className="text-sm text-muted-foreground mb-4">Add games to your favorites to see them here</p>
                    <Link href="/" className="bg-primary hover:bg-primary/90 text-white rounded px-4 py-2">
                      Explore Games
                    </Link>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="recently-played">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {recentlyPlayed.map((game) => (
                    <GameCard key={game.id} {...game} />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </Layout>
  );
} 