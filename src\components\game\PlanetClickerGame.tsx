"use client";

import React, { useState } from 'react';
import { Play, Maximize2, Minimize2, ExternalLink } from 'lucide-react';

const PlanetClickerGame = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [showGame, setShowGame] = useState(false);

  const gameUrl = "https://www.coolmathgames.com/0-planet-clicker/play";

  const handlePlayClick = () => {
    setShowGame(true);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const openInNewTab = () => {
    window.open(gameUrl, '_blank');
  };

  return (
    <div className="mb-12">
      {/* 游戏标题区域 */}
      <div className="text-center mb-6">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-cosmic-purple via-cosmic-blue to-cosmic-nebula bg-clip-text text-transparent mb-2">
          🪐 Featured: Planet Clicker
        </h2>
        <p className="text-muted-foreground">
          Click your way through the cosmos and build your planetary empire!
        </p>
      </div>

      {/* 游戏容器 */}
      <div className={`relative mx-auto transition-all duration-500 ${
        isExpanded 
          ? 'fixed inset-4 z-50 bg-black rounded-lg' 
          : 'max-w-4xl'
      }`}>
        
        {/* 游戏预览/播放区域 */}
        {!showGame ? (
          <div className="relative aspect-video game-preview-bg rounded-lg overflow-hidden star-shimmer">
            {/* 背景装饰 */}
            <div className="absolute inset-0 bg-gradient-to-br from-black/30 to-transparent"></div>
            
            {/* 游戏预览内容 */}
            <div className="relative z-10 flex flex-col items-center justify-center h-full text-center p-8">
              <div className="cosmic-glow rounded-full p-6 mb-6">
                <div className="text-6xl planet-orbit">🪐</div>
              </div>
              
              <h3 className="text-2xl font-bold text-white mb-4">
                Planet Clicker
              </h3>
              
              <p className="text-muted-foreground mb-8 max-w-md">
                Start with a small planet and click your way to cosmic domination. 
                Upgrade your planets, unlock new worlds, and become the ultimate space tycoon!
              </p>
              
              <button
                onClick={handlePlayClick}
                className="cosmic-button text-white px-8 py-4 rounded-lg flex items-center gap-3 text-lg font-semibold"
              >
                <Play size={24} />
                Play Planet Clicker
              </button>
            </div>

            {/* 星空装饰 */}
            <div className="absolute top-4 left-8 w-1 h-1 bg-white rounded-full animate-pulse"></div>
            <div className="absolute top-12 right-12 w-1 h-1 bg-white rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
            <div className="absolute bottom-8 left-16 w-1 h-1 bg-white rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
            <div className="absolute bottom-16 right-8 w-1 h-1 bg-white rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
          </div>
        ) : (
          /* 游戏iframe区域 */
          <div className="relative">
            {/* 控制栏 */}
            <div className="flex justify-between items-center bg-card p-3 rounded-t-lg border-b border-border">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="ml-4 text-sm font-medium text-white">Planet Clicker</span>
              </div>
              
              <div className="flex items-center gap-2">
                <button
                  onClick={toggleExpanded}
                  className="game-control-btn p-2 rounded transition-all"
                  title={isExpanded ? "Exit Fullscreen" : "Fullscreen"}
                >
                  {isExpanded ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
                </button>

                <button
                  onClick={openInNewTab}
                  className="game-control-btn p-2 rounded transition-all"
                  title="Open in New Tab"
                >
                  <ExternalLink size={16} />
                </button>
              </div>
            </div>

            {/* iframe容器 */}
            <div className="relative game-iframe-container rounded-b-lg overflow-hidden">
              {!isLoaded && (
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-cosmic-purple/20 to-cosmic-blue/20">
                  <div className="text-center">
                    <div className="cosmic-glow rounded-full p-4 mb-4 inline-block">
                      <div className="text-4xl planet-orbit">🪐</div>
                    </div>
                    <p className="text-white">Loading Planet Clicker...</p>
                  </div>
                </div>
              )}
              
              <iframe
                src={gameUrl}
                className={`w-full transition-opacity duration-500 ${
                  isExpanded ? 'h-[calc(100vh-8rem)]' : 'aspect-video'
                } ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
                frameBorder="0"
                allowFullScreen
                title="Planet Clicker Game"
                onLoad={() => setIsLoaded(true)}
              />
            </div>
          </div>
        )}

        {/* 全屏模式背景遮罩 */}
        {isExpanded && (
          <div
            className="fixed inset-0 bg-black/80 z-40 fullscreen-overlay"
            onClick={toggleExpanded}
          />
        )}
      </div>

      {/* 游戏信息 */}
      {showGame && (
        <div className="max-w-4xl mx-auto mt-6 p-4 bg-card rounded-lg border border-border">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex-1">
              <h4 className="font-semibold text-white mb-2">About Planet Clicker</h4>
              <p className="text-sm text-muted-foreground">
                Experience the addictive gameplay of Planet Clicker right here! 
                Click to generate energy, upgrade your planets, and expand your cosmic empire. 
                Perfect for quick gaming sessions or extended space exploration.
              </p>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setShowGame(false)}
                className="px-4 py-2 bg-muted hover:bg-muted/80 rounded text-sm transition-colors"
              >
                Hide Game
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlanetClickerGame;
