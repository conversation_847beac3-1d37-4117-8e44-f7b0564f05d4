"use client";

import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import GameCard from '@/components/game/GameCard';
import Link from 'next/link';
import { Heart, X } from 'lucide-react';

// 收藏的游戏结构
interface FavoriteGame {
  id: string;
  title: string;
  slug: string;
  image: string;
  addedAt: string;
}

export default function FavoritesPage() {
  const [favorites, setFavorites] = useState<FavoriteGame[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 在客户端加载收藏的游戏
  useEffect(() => {
    // 从localStorage读取收藏的游戏
    const loadFavorites = () => {
      try {
        const storedFavorites = localStorage.getItem('favoriteGames');
        if (storedFavorites) {
          // 解析JSON并按添加时间降序排序（最新添加的在前面）
          const parsedFavorites: FavoriteGame[] = JSON.parse(storedFavorites);
          parsedFavorites.sort((a, b) => 
            new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime()
          );
          setFavorites(parsedFavorites);
        }
      } catch (error) {
        console.error('Error loading favorites:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadFavorites();

    // 添加事件监听器，以便在其他页面更新收藏时刷新
    window.addEventListener('storage', loadFavorites);
    
    // 清理事件监听器
    return () => {
      window.removeEventListener('storage', loadFavorites);
    };
  }, []);

  // 从收藏中移除游戏
  const removeFromFavorites = (gameId: string) => {
    try {
      // 获取当前收藏
      const currentFavorites = [...favorites];
      // 过滤掉要移除的游戏
      const updatedFavorites = currentFavorites.filter(game => game.id !== gameId);
      // 更新状态
      setFavorites(updatedFavorites);
      // 保存到localStorage
      localStorage.setItem('favoriteGames', JSON.stringify(updatedFavorites));
      // 触发storage事件，以便其他页面同步更新
      window.dispatchEvent(new Event('storage'));
    } catch (error) {
      console.error('Error removing game from favorites:', error);
    }
  };

  return (
    <Layout>
      <div className="max-w-screen-xl mx-auto px-4 py-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4 sm:mb-6">
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold">My Favorite Games</h1>
          <Link 
            href="/" 
            className="bg-primary hover:bg-primary/90 text-white rounded px-3 py-1.5 sm:px-4 sm:py-2 text-sm font-medium inline-flex items-center justify-center"
          >
            Explore More Games
          </Link>
        </div>

        {isLoading ? (
          <div className="text-center py-8 sm:py-12">
            <div className="animate-pulse flex justify-center">
              <Heart className="h-8 w-8 text-muted-foreground" />
            </div>
            <p className="mt-4">Loading your favorites...</p>
          </div>
        ) : favorites.length > 0 ? (
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-6">
            {favorites.map(game => (
              <div key={game.id} className="relative group">
                <GameCard
                  id={game.id}
                  title={game.title}
                  slug={game.slug}
                  image={game.image}
                />
                <button 
                  onClick={() => removeFromFavorites(game.id)}
                  className="absolute top-1 right-1 sm:top-2 sm:right-2 bg-black/70 hover:bg-black p-1.5 sm:p-2 rounded-full opacity-80 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity"
                  title="Remove from favorites"
                  aria-label="Remove from favorites"
                >
                  <X className="w-4 h-4 sm:w-5 sm:h-5 text-red-400" />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 sm:py-12 bg-card rounded-lg">
            <Heart className="mx-auto w-10 h-10 sm:w-12 sm:h-12 text-muted-foreground mb-4" />
            <h2 className="text-lg sm:text-xl font-bold mb-2">No Favorites Yet</h2>
            <p className="text-muted-foreground mb-6 px-4 sm:px-6 max-w-md mx-auto text-sm sm:text-base">
              You haven't added any games to your favorites yet. Browse our collection and add games you love!
            </p>
            <Link
              href="/"
              className="bg-primary hover:bg-primary/90 text-white rounded px-5 py-2 sm:px-6 sm:py-3 inline-block text-sm sm:text-base font-medium"
            >
              Find Games to Play
            </Link>
          </div>
        )}
      </div>
    </Layout>
  );
} 