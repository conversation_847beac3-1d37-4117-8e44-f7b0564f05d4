---
description:
globs:
alwaysApply: false
---
# 编码标准

## 通用规范

- 使用TypeScript进行类型声明
- 使用ES6+语法特性
- 代码缩进使用2个空格
- 使用分号作为语句结束符
- 使用单引号作为字符串定界符

## 命名规范

- 组件文件名: PascalCase (例如 `GameCard.tsx`)
- 工具函数文件名: camelCase (例如 `fetchData.ts`)
- 页面文件: `page.tsx` (Next.js约定)
- React组件: PascalCase (例如 `export default function GameCard()`)
- 函数和变量: camelCase (例如 `const fetchGameData = () => {}`)
- 常量: UPPER_SNAKE_CASE (例如 `const API_BASE_URL = 'https://...'`)

## 组件结构

- 使用函数组件和React Hooks
- 每个组件应该只做一件事，保持组件的单一职责
- 复用组件时优先考虑组合而非继承
- 使用Tailwind CSS进行样式设计

## 代码组织

- 相关功能应该放在同一个文件夹中
- 每个组件应该在其自己的文件中定义
- 公共组件应放在`components`目录下
- 工具函数应放在`lib`目录下
- 配置文件应放在`config`目录下

## 性能优化

- 使用`useMemo`和`useCallback`来优化渲染性能
- 使用Next.js的Image组件优化图片加载
- 合理使用Next.js的SSR, SSG和ISR特性
- 延迟加载非关键资源
- 适当使用懒加载组件

## SEO最佳实践

- 每个页面都应该有唯一的标题和元描述
- 使用语义化HTML标签
- 确保页面响应式设计，适配移动端
- 提供适当的替代文本(alt text)给图片
- 使用规范化URL (canonical URL)
