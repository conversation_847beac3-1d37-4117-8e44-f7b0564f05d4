import Link from 'next/link';
import Layout from '@/components/layout/Layout';

// 标签数据接口
interface ApiTag {
  id: number;
  name: string;
  url: string;
}

// API响应接口
interface ApiResponse {
  status: string;
  data: ApiTag[];
}

// 每页显示标签数量
const TAGS_PER_PAGE = 30;

// 获取标签图标的函数
function getTagIcon(tagName: string) {
  // 根据标签名称选择对应的图标
  const tagLower = tagName.toLowerCase();
  
  if (tagLower.includes('action') || tagLower.includes('adventure')) return '🎮';
  if (tagLower.includes('puzzle')) return '🧩';
  if (tagLower.includes('racing') || tagLower.includes('car')) return '🏎️';
  if (tagLower.includes('sport')) return '⚽';
  if (tagLower.includes('shooting') || tagLower.includes('gun')) return '🎯';
  if (tagLower.includes('strategy') || tagLower.includes('brain')) return '🧠';
  if (tagLower.includes('arcade')) return '🕹️';
  if (tagLower.includes('multiplayer') || tagLower.includes('player')) return '👥';
  if (tagLower.includes('zombie')) return '🧟';
  if (tagLower.includes('girl') || tagLower.includes('princess')) return '👸';
  if (tagLower.includes('magic') || tagLower.includes('wizard')) return '✨';
  if (tagLower.includes('animal')) return '🐾';
  if (tagLower.includes('platform')) return '📚';
  if (tagLower.includes('card')) return '🃏';
  if (tagLower.includes('drawing')) return '🎨';
  
  // 默认图标
  return '🎲';
}

// 获取所有标签
async function fetchTags(): Promise<ApiTag[]> {
  try {
    const apiBaseUrl = 'https://api.planetclicker.pro';
    const response = await fetch(`${apiBaseUrl}/tags`, { 
      next: { revalidate: 3600 } // 缓存1小时
    });
    
    console.log('正在获取所有标签');
    
    if (!response.ok) {
      throw new Error(`获取标签失败: ${response.status}`);
    }
    
    const data: ApiResponse = await response.json();
    
    if (data.status === 'success' && Array.isArray(data.data)) {
      console.log(`成功获取 ${data.data.length} 个标签`);
      return data.data;
    }
    
    console.error('API响应中没有标签数据');
    return [];
  } catch (error) {
    console.error('获取标签时出错:', error);
    return [];
  }
}

// 分页组件
function Pagination({ 
  currentPage, 
  totalPages 
}: { 
  currentPage: number; 
  totalPages: number;
}) {
  return (
    <div className="flex justify-center mt-8 mb-4">
      <div className="flex flex-wrap justify-center gap-1">
        {currentPage > 1 && (
          <Link
            href={`/tags?page=${currentPage - 1}`}
            className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
          >
            Prev
          </Link>
        )}
        
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          // Display page numbers around current page
          let pageNum = 1;
          if (totalPages <= 5) {
            pageNum = i + 1;
          } else if (currentPage <= 3) {
            pageNum = i + 1;
          } else if (currentPage >= totalPages - 2) {
            pageNum = totalPages - 4 + i;
          } else {
            pageNum = currentPage - 2 + i;
          }
          
          // Hide some page numbers on smallest screens
          const hideOnMobile = (i === 0 || i === 4) && totalPages > 3;
          
          return (
            <Link
              key={pageNum}
              href={`/tags?page=${pageNum}`}
              className={`${hideOnMobile ? 'hidden sm:block' : ''} px-2 sm:px-4 py-2 text-sm sm:text-base rounded ${
                currentPage === pageNum
                  ? 'bg-primary text-white'
                  : 'bg-card hover:bg-card/80 text-muted-foreground hover:text-white'
              }`}
            >
              {pageNum}
            </Link>
          );
        })}
        
        {currentPage < totalPages && (
          <Link
            href={`/tags?page=${currentPage + 1}`}
            className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
          >
            Next
          </Link>
        )}
      </div>
    </div>
  );
}

export default async function TagsPage({ searchParams }: { searchParams: { page?: string } }) {
  // 确保searchParams已经解析完成
  const resolvedParams = await Promise.resolve(searchParams);
  const pageParam = resolvedParams.page || '1';
  const currentPage = parseInt(pageParam, 10);
  
  // 获取标签列表
  const tags = await fetchTags();
  
  // 计算总页数
  const totalPages = Math.ceil(tags.length / TAGS_PER_PAGE);
  
  // 获取当前页的标签
  const startIndex = (currentPage - 1) * TAGS_PER_PAGE;
  const currentTags = tags.slice(startIndex, startIndex + TAGS_PER_PAGE);
  
  return (
    <Layout>
      <div className="max-w-screen-xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold mb-4">All Tags</h1>
          <p className="text-muted-foreground">
            Browse games by tags. We have {tags.length} different tags to help you find your perfect game.
          </p>
        </div>
        
        {currentTags.length > 0 ? (
          <>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {currentTags.map((tag) => (
                <Link
                  key={tag.id}
                  href={`/tag/${tag.url}`}
                  className="bg-card hover:bg-card/80 p-4 rounded-lg flex items-center gap-3 transition-colors"
                >
                  <span className="text-2xl" aria-hidden="true">{getTagIcon(tag.name)}</span>
                  <span className="font-medium">{tag.name}</span>
                </Link>
              ))}
            </div>
            
            {totalPages > 1 && (
              <Pagination 
                currentPage={currentPage} 
                totalPages={totalPages}
              />
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-muted-foreground">No tags found</h3>
            <p className="mt-2">Check back later for new tags</p>
          </div>
        )}
      </div>
    </Layout>
  );
} 