"use client";

import React from 'react';
import Link from 'next/link';
import { TiktokIcon, DiscordIcon, LinkedinIcon, YoutubeIcon } from './SocialIcons';

const Footer = () => {
  return (
    <footer className="text-muted-foreground py-8 mt-12 border-t border-border relative"
            style={{
              background: 'linear-gradient(135deg, hsl(var(--card)) 0%, hsl(240, 30%, 8%) 100%)',
              boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.3)'
            }}>
      {/* 星空装饰 */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-4 left-10 w-1 h-1 bg-cosmic-starGlow rounded-full animate-pulse"></div>
        <div className="absolute top-8 right-20 w-1 h-1 bg-cosmic-starGlow rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-10 left-1/4 w-1 h-1 bg-cosmic-starGlow rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-6 right-1/3 w-1 h-1 bg-cosmic-starGlow rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
      </div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-wrap gap-6 justify-between mb-6">
          <div className="space-y-2">
            <h3 className="text-white font-medium bg-gradient-to-r from-cosmic-purple to-cosmic-blue bg-clip-text text-transparent">About</h3>
            <ul className="space-y-1 text-sm">
              <li><Link href="/pages/about" className="hover:text-white transition-all duration-200 hover:text-shadow-cosmic">About Us</Link></li>
              <li><Link href="/pages/contact" className="hover:text-white transition-all duration-200 hover:text-shadow-cosmic">Contact</Link></li>
            </ul>
          </div>

          <div className="space-y-2">
            <h3 className="text-white font-medium bg-gradient-to-r from-cosmic-purple to-cosmic-blue bg-clip-text text-transparent">Legal</h3>
            <ul className="space-y-1 text-sm">
              <li><Link href="/pages/terms" className="hover:text-white transition-all duration-200 hover:text-shadow-cosmic">Terms & Conditions</Link></li>
              <li><Link href="/pages/privacy" className="hover:text-white transition-all duration-200 hover:text-shadow-cosmic">Privacy Policy</Link></li>
            </ul>
          </div>

          <div className="space-y-2">
            <h3 className="text-white font-medium bg-gradient-to-r from-cosmic-purple to-cosmic-blue bg-clip-text text-transparent">Games</h3>
            <ul className="space-y-1 text-sm">
              <li><Link href="/category/action-games" className="hover:text-white transition-all duration-200 hover:text-shadow-cosmic">Action Games</Link></li>
              <li><Link href="/category/puzzle-games" className="hover:text-white transition-all duration-200 hover:text-shadow-cosmic">Puzzle Games</Link></li>
              <li><Link href="/category/shooting-games" className="hover:text-white transition-all duration-200 hover:text-shadow-cosmic">Shooting Games</Link></li>
              <li><Link href="/category/multiplayer-games" className="hover:text-white transition-all duration-200 hover:text-shadow-cosmic">Multiplayer Games</Link></li>
            </ul>
          </div>

          {/* <div className="space-y-2">
            <h3 className="text-white font-medium">Language</h3>
            <ul className="space-y-1 text-sm">
              <li><Link href="/" className="hover:text-white transition-colors">English</Link></li>
              <li><Link href="/es" className="hover:text-white transition-colors">Español</Link></li>
              <li><Link href="/fr" className="hover:text-white transition-colors">Français</Link></li>
              <li><Link href="/de" className="hover:text-white transition-colors">Deutsch</Link></li>
            </ul>
          </div> */}

          <div className="space-y-4">
            <h3 className="text-white font-medium bg-gradient-to-r from-cosmic-purple to-cosmic-blue bg-clip-text text-transparent">Follow us</h3>
            <div className="flex gap-3">
              <Link href="https://www.tiktok.com" className="text-muted-foreground hover:text-white transition-all duration-200 hover:scale-110 hover:drop-shadow-lg" style={{ filter: 'drop-shadow(0 0 8px rgba(138, 43, 226, 0))' }}>
                <TiktokIcon className="h-5 w-5" />
              </Link>
              <Link href="https://discord.com" className="text-muted-foreground hover:text-white transition-all duration-200 hover:scale-110 hover:drop-shadow-lg" style={{ filter: 'drop-shadow(0 0 8px rgba(138, 43, 226, 0))' }}>
                <DiscordIcon className="h-5 w-5" />
              </Link>
              <Link href="https://linkedin.com" className="text-muted-foreground hover:text-white transition-all duration-200 hover:scale-110 hover:drop-shadow-lg" style={{ filter: 'drop-shadow(0 0 8px rgba(138, 43, 226, 0))' }}>
                <LinkedinIcon className="h-5 w-5" />
              </Link>
              <Link href="https://youtube.com" className="text-muted-foreground hover:text-white transition-all duration-200 hover:scale-110 hover:drop-shadow-lg" style={{ filter: 'drop-shadow(0 0 8px rgba(138, 43, 226, 0))' }}>
                <YoutubeIcon className="h-5 w-5" />
              </Link>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-center border-t border-border pt-4 gap-4">
          <p className="text-xs">© {new Date().getFullYear()} Planet Clicker  <Link href="https://www.planetclicker.pro" className="hover:text-white transition-colors">www.planetclicker.pro</Link></p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
