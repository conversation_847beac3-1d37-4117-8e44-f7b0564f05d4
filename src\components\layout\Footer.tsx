"use client";

import React from 'react';
import Link from 'next/link';
import { TiktokIcon, DiscordIcon, LinkedinIcon, YoutubeIcon } from './SocialIcons';

const Footer = () => {
  return (
    <footer className="bg-card text-muted-foreground py-6 mt-12 border-t border-border">
      <div className="container mx-auto px-4">
        <div className="flex flex-wrap gap-6 justify-between mb-6">
          <div className="space-y-2">
            <h3 className="text-white font-medium">About</h3>
            <ul className="space-y-1 text-sm">
              <li><Link href="/pages/about" className="hover:text-white transition-colors">About Us</Link></li>
              <li><Link href="/pages/contact" className="hover:text-white transition-colors">Contact</Link></li>
            </ul>
          </div>

          <div className="space-y-2">
            <h3 className="text-white font-medium">Legal</h3>
            <ul className="space-y-1 text-sm">
              <li><Link href="/pages/terms" className="hover:text-white transition-colors">Terms & Conditions</Link></li>
              <li><Link href="/pages/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
            </ul>
          </div>

          <div className="space-y-2">
            <h3 className="text-white font-medium">Games</h3>
            <ul className="space-y-1 text-sm">
              <li><Link href="/category/action-games" className="hover:text-white transition-colors">Action Games</Link></li>
              <li><Link href="/category/puzzle-games" className="hover:text-white transition-colors">Puzzle Games</Link></li>
              <li><Link href="/category/shooting-games" className="hover:text-white transition-colors">Shooting Games</Link></li>
              <li><Link href="/category/multiplayer-games" className="hover:text-white transition-colors">Multiplayer Games</Link></li>
            </ul>
          </div>

          {/* <div className="space-y-2">
            <h3 className="text-white font-medium">Language</h3>
            <ul className="space-y-1 text-sm">
              <li><Link href="/" className="hover:text-white transition-colors">English</Link></li>
              <li><Link href="/es" className="hover:text-white transition-colors">Español</Link></li>
              <li><Link href="/fr" className="hover:text-white transition-colors">Français</Link></li>
              <li><Link href="/de" className="hover:text-white transition-colors">Deutsch</Link></li>
            </ul>
          </div> */}

          <div className="space-y-4">
            <h3 className="text-white font-medium">Follow us</h3>
            <div className="flex gap-3">
              <Link href="https://www.tiktok.com" className="text-muted-foreground hover:text-white transition-colors">
                <TiktokIcon className="h-5 w-5" />
              </Link>
              <Link href="https://discord.com" className="text-muted-foreground hover:text-white transition-colors">
                <DiscordIcon className="h-5 w-5" />
              </Link>
              <Link href="https://linkedin.com" className="text-muted-foreground hover:text-white transition-colors">
                <LinkedinIcon className="h-5 w-5" />
              </Link>
              <Link href="https://youtube.com" className="text-muted-foreground hover:text-white transition-colors">
                <YoutubeIcon className="h-5 w-5" />
              </Link>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-center border-t border-border pt-4 gap-4">
          <p className="text-xs">© {new Date().getFullYear()} H5Play  <Link href="https://www.h5play.com" className="hover:text-white transition-colors">www.h5play.com</Link></p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
