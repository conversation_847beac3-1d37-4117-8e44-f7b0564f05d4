"use client";

import { useState } from 'react';
import { Star, StarHalf } from 'lucide-react';

interface GameRatingProps {
  rating: string;
  totalRatings: number;
  gameTitle: string;
}

const GameRating = ({ rating, totalRatings, gameTitle }: GameRatingProps) => {
  const [userRating, setUserRating] = useState<number | null>(null);
  const [hoverRating, setHoverRating] = useState<number | null>(null);
  const [hasRated, setHasRated] = useState(false);

  const handleRatingClick = (rating: number) => {
    setUserRating(rating);
    setHasRated(true);
  };

  const ratingFloat = parseFloat(rating);
  
  // Generate stars based on rating
  const renderStars = () => {
    const stars = [];
    const fullStars = Math.floor(ratingFloat);
    const hasHalfStar = ratingFloat % 1 >= 0.5;

    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(
          <Star key={i} className="fill-yellow-400 text-yellow-400" size={24} />
        );
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(
          <StarHalf key={i} className="fill-yellow-400 text-yellow-400" size={24} />
        );
      } else {
        stars.push(
          <Star key={i} className="text-gray-400" size={24} />
        );
      }
    }

    return stars;
  };

  // User rating stars
  const renderUserRatingStars = () => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onMouseEnter={() => setHoverRating(star)}
            onMouseLeave={() => setHoverRating(null)}
            onClick={() => handleRatingClick(star)}
            disabled={hasRated}
            className="focus:outline-none transition-colors"
          >
            <Star
              size={28}
              className={`${
                (hoverRating !== null ? star <= hoverRating : star <= (userRating || 0))
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'text-gray-400'
              } ${!hasRated && 'cursor-pointer'}`}
            />
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className="bg-card rounded-lg p-4 mb-4">
      <h3 className="font-bold mb-3">Rating</h3>
      
      <div className="flex items-center gap-2 mb-3">
        <div className="flex">{renderStars()}</div>
        <span className="text-xl font-bold">{rating}</span>
        <span className="text-sm text-muted-foreground">({totalRatings})</span>
      </div>
      
      <div className="mb-4">
        <div className="flex justify-between text-sm mb-1">
          <span>5 ★</span>
          <div className="w-48 bg-gray-700 h-2 rounded-full overflow-hidden">
            <div className="bg-yellow-400 h-full" style={{ width: '70%' }}></div>
          </div>
          <span className="text-muted-foreground">70%</span>
        </div>
        <div className="flex justify-between text-sm mb-1">
          <span>4 ★</span>
          <div className="w-48 bg-gray-700 h-2 rounded-full overflow-hidden">
            <div className="bg-yellow-400 h-full" style={{ width: '20%' }}></div>
          </div>
          <span className="text-muted-foreground">20%</span>
        </div>
        <div className="flex justify-between text-sm mb-1">
          <span>3 ★</span>
          <div className="w-48 bg-gray-700 h-2 rounded-full overflow-hidden">
            <div className="bg-yellow-400 h-full" style={{ width: '8%' }}></div>
          </div>
          <span className="text-muted-foreground">8%</span>
        </div>
        <div className="flex justify-between text-sm mb-1">
          <span>2 ★</span>
          <div className="w-48 bg-gray-700 h-2 rounded-full overflow-hidden">
            <div className="bg-yellow-400 h-full" style={{ width: '1%' }}></div>
          </div>
          <span className="text-muted-foreground">1%</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>1 ★</span>
          <div className="w-48 bg-gray-700 h-2 rounded-full overflow-hidden">
            <div className="bg-yellow-400 h-full" style={{ width: '1%' }}></div>
          </div>
          <span className="text-muted-foreground">1%</span>
        </div>
      </div>
      
      <div className="border-t border-border pt-4">
        <h4 className="font-medium mb-2">Rate {gameTitle}</h4>
        {hasRated ? (
          <div className="text-center py-2">
            <p className="text-green-400 font-medium">Thanks for your rating!</p>
          </div>
        ) : (
          <div>
            {renderUserRatingStars()}
            <p className="text-xs text-muted-foreground mt-2">
              Click on a star to rate this game
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GameRating; 