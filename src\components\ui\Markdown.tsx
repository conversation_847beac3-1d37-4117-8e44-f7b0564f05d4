"use client";

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

interface MarkdownProps {
  content: string;
  className?: string;
}

const Markdown: React.FC<MarkdownProps> = ({ content, className = '' }) => {
  return (
    <div className={`prose prose-invert max-w-none ${className} 
      prose-img:rounded prose-img:w-full prose-img:mx-auto
      prose-headings:scroll-mt-8
      prose-a:text-blue-400 prose-a:hover:text-blue-300
      prose-p:break-words
      prose-table:overflow-x-auto prose-table:block
      prose-pre:overflow-x-auto prose-pre:text-xs prose-pre:sm:text-sm
    `}>
      <ReactMarkdown 
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default Markdown; 