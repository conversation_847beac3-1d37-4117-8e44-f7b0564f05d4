import React, { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, InfoIcon } from 'lucide-react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Create toast style variants
const toastVariants = cva(
  "fixed z-50 flex items-center gap-2 rounded-md px-4 py-3 shadow-lg transition-all duration-300",
  {
    variants: {
      variant: {
        success: "bg-green-600 text-white",
        error: "bg-red-600 text-white",
        warning: "bg-yellow-600 text-white",
        info: "bg-blue-600 text-white",
      },
      position: {
        'top-right': "top-4 right-4",
        'top-left': "top-4 left-4",
        'bottom-right': "bottom-4 right-4",
        'bottom-left': "bottom-4 left-4",
        'top-center': "top-4 left-1/2 -translate-x-1/2",
        'bottom-center': "bottom-4 left-1/2 -translate-x-1/2",
      },
    },
    defaultVariants: {
      variant: "info",
      position: "bottom-right",
    },
  }
);

// Toast component properties
export interface ToastProps extends VariantProps<typeof toastVariants> {
  message: string;
  visible: boolean;
  duration?: number;
  onClose: () => void;
  className?: string;
}

export function Toast({
  message,
  visible,
  variant,
  position,
  duration = 3000,
  onClose,
  className,
}: ToastProps) {
  const [isVisible, setIsVisible] = useState(visible);

  // Update internal state when visible property changes
  useEffect(() => {
    setIsVisible(visible);
    
    // If toast is visible, set auto-close timer
    if (visible && duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onClose();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [visible, duration, onClose]);

  // If toast is not visible, don't render anything
  if (!isVisible) return null;

  // Determine icon based on variant
  const IconComponent = () => {
    switch (variant) {
      case 'success':
        return <CheckCircle className="w-5 h-5" />;
      case 'error':
        return <X className="w-5 h-5" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5" />;
      case 'info':
      default:
        return <InfoIcon className="w-5 h-5" />;
    }
  };

  return (
    <div 
      className={cn(
        toastVariants({ variant, position }), 
        className
      )}
      role="alert"
    >
      <IconComponent />
      <p className="text-sm font-medium">{message}</p>
      <button 
        onClick={() => {
          setIsVisible(false);
          onClose();
        }}
        className="ml-2 text-white/80 hover:text-white"
        aria-label="Close"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  );
}

// Toast container component for managing multiple toasts
export interface ToastItem {
  id: string;
  message: string;
  variant?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

export interface ToastContainerProps {
  toasts: ToastItem[];
  position?: VariantProps<typeof toastVariants>['position'];
  onRemove: (id: string) => void;
}

export function ToastContainer({
  toasts,
  position = 'bottom-right',
  onRemove,
}: ToastContainerProps) {
  return (
    <div className="toast-container">
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          message={toast.message}
          variant={toast.variant}
          position={position}
          duration={toast.duration}
          visible={true}
          onClose={() => onRemove(toast.id)}
          className="mb-2"
        />
      ))}
    </div>
  );
} 