---
description:
globs:
alwaysApply: false
---
# 组件指南

## 游戏卡片组件

游戏卡片组件用于在游戏列表中展示单个游戏的信息。

```tsx
// 用法示例
import GameCard from '@/components/game/GameCard';

<GameCard 
  game={gameObject} 
  className="optional-tailwind-classes" 
/>
```

## 游戏网格组件

游戏网格组件用于展示游戏卡片的网格布局。

```tsx
// 用法示例
import GameGrid from '@/components/game/GameGrid';

<GameGrid 
  games={gamesArray} 
  title="最新游戏" 
  className="optional-tailwind-classes" 
/>
```

## 分类卡片组件

分类卡片组件用于展示游戏分类。

```tsx
// 用法示例
import CategoryCard from '@/components/layout/CategoryCard';

<CategoryCard 
  category={categoryObject} 
  className="optional-tailwind-classes" 
/>
```

## 标签组件

标签组件用于显示游戏标签。

```tsx
// 用法示例
import TagBadge from '@/components/ui/TagBadge';

<TagBadge 
  tag={tagObject} 
  className="optional-tailwind-classes" 
/>
```

## 分页组件

分页组件用于列表页面的分页导航。

```tsx
// 用法示例
import Pagination from '@/components/ui/Pagination';

<Pagination 
  currentPage={1} 
  totalPages={10} 
  onPageChange={(page) => setPage(page)} 
/>
```

## 搜索组件

搜索组件用于游戏搜索功能。

```tsx
// 用法示例
import SearchBox from '@/components/layout/SearchBox';

<SearchBox 
  initialValue="" 
  onSearch={(keyword) => handleSearch(keyword)} 
/>
```

## 导航组件

导航组件用于站点导航。

```tsx
// 用法示例
import NavBar from '@/components/layout/NavBar';

<NavBar 
  activeItem="home" 
/>
```

## 页脚组件

页脚组件用于站点底部信息。

```tsx
// 用法示例
import Footer from '@/components/layout/Footer';

<Footer />
```
