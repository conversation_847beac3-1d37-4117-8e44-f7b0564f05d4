import React from 'react';
import FeatureIcon from './FeatureIcon';

interface PlatformFeature {
  type: 'games' | 'install' | 'device' | 'friends' | 'free';
  text: string;
}

const PlatformFeatures = () => {
  const features: PlatformFeature[] = [
    { type: 'games', text: '4000+ games' },
    { type: 'install', text: 'No install needed' },
    { type: 'device', text: 'On any device' },
    { type: 'friends', text: 'Play with friends' },
    { type: 'free', text: 'All for free' }
  ];

  return (
    <div className="flex flex-wrap justify-center gap-6 md:gap-8 lg:gap-10">
      {features.map((feature) => (
        <div key={feature.text} className="flex items-center gap-2">
          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
            <FeatureIcon type={feature.type} size={22} />
          </div>
          <span className="text-sm font-medium whitespace-nowrap">{feature.text}</span>
        </div>
      ))}
    </div>
  );
};

export default PlatformFeatures; 