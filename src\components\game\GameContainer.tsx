import React, { useRef, useEffect, useState, forwardRef, useImperativeHandle } from 'react';

interface GameContainerProps {
  src: string;
  isPlaying: boolean;
  gameType?: string;
  width?: number;
  height?: number;
}

// 定义组件引用的方法类型
export interface GameContainerRef {
  requestFullscreen: () => Promise<void>;
  exitFullscreen: () => Promise<void>;
}

/**
 * 游戏容器组件，处理游戏尺寸和滚动条问题
 */
const GameContainer = forwardRef<GameContainerRef, GameContainerProps>(({
  src,
  isPlaying,
  gameType = 'html5',
  width,
  height
}, ref) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  
  // 向父组件暴露方法
  useImperativeHandle(ref, () => ({
    // 请求全屏
    requestFullscreen: async () => {
      if (iframeRef.current) {
        try {
          await iframeRef.current.requestFullscreen();
          return Promise.resolve();
        } catch (error) {
          console.error('Failed to enter fullscreen mode:', error);
          return Promise.reject(error);
        }
      }
      return Promise.reject(new Error('Iframe reference not available'));
    },
    // 退出全屏
    exitFullscreen: async () => {
      if (document.fullscreenElement) {
        try {
          await document.exitFullscreen();
          return Promise.resolve();
        } catch (error) {
          console.error('Failed to exit fullscreen mode:', error);
          return Promise.reject(error);
        }
      }
      return Promise.resolve();
    }
  }));
  
  // 计算最佳游戏尺寸，保持宽高比并不超出容器大小
  const calculateOptimalSize = () => {
    if (!containerRef.current) return;
    
    const containerWidth = containerRef.current.clientWidth;
    const containerHeight = containerRef.current.clientHeight;
    
    // 使用游戏的原始尺寸，如果提供的话
    const gameWidth = width || containerWidth;
    const gameHeight = height || containerHeight;
    
    // 计算宽高比
    const aspectRatio = gameWidth / gameHeight;
    
    let optimalWidth = containerWidth;
    let optimalHeight = containerWidth / aspectRatio;
    
    // 如果高度超出容器，则以高度为准
    if (optimalHeight > containerHeight) {
      optimalHeight = containerHeight;
      optimalWidth = containerHeight * aspectRatio;
    }
    
    setContainerSize({
      width: optimalWidth,
      height: optimalHeight
    });
  };
  
  // 在组件挂载和窗口大小改变时计算尺寸
  useEffect(() => {
    calculateOptimalSize();
    
    const handleResize = () => {
      calculateOptimalSize();
    };
    
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [width, height]);
  
  // 使用postMessage与游戏内容通信，禁用滚动条
  useEffect(() => {
    if (isPlaying && iframeRef.current) {
      // 尝试使用postMessage禁用游戏内部滚动条
      const disableScrollbarsMessage = {
        action: 'disableScrollbars',
        value: true
      };
      
      try {
        // 延迟一点时间确保iframe已加载
        const timer = setTimeout(() => {
          iframeRef.current?.contentWindow?.postMessage(
            JSON.stringify(disableScrollbarsMessage),
            '*'
          );
        }, 1000);
        
        return () => clearTimeout(timer);
      } catch (error) {
        console.error('Failed to communicate with game iframe:', error);
      }
    }
  }, [isPlaying]);

  if (!isPlaying) return null;
  
  return (
    <div 
      ref={containerRef} 
      className="w-full h-full aspect-video relative overflow-hidden"
    >
      <iframe
        ref={iframeRef}
        src={src}
        className="absolute inset-0"
        style={{
          width: '100%',
          height: '100%',
          overflow: 'hidden',
          border: 0,
          display: 'block'
        }}
        allowFullScreen
        allow="autoplay; fullscreen"
        scrolling="no"
        frameBorder="0"
      />
      
      {/* 创建一个覆盖层，阻止滚动条交互 */}
      <div 
        className="absolute inset-0 pointer-events-none" 
        style={{ 
          zIndex: 1,
          // 这个透明遮罩层阻止鼠标与滚动条交互，但不影响游戏操作
          pointerEvents: 'none'
        }}
      />
    </div>
  );
});

GameContainer.displayName = 'GameContainer';

export default GameContainer; 