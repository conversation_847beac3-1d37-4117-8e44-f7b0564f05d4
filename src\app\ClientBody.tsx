"use client";

import { useEffect } from "react";
import { usePathname } from 'next/navigation';

export default function ClientBody({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // 客户端水合后确保类名一致性
  useEffect(() => {
    // 确保我们的类名包含字体相关的类
    document.body.className = "font-sans antialiased bg-background text-foreground";
  }, []);

  // 监听路由变化并上报百度统计
  useEffect(() => {
    if (typeof window !== 'undefined' && (window as any)._hmt) {
      (window as any)._hmt.push(['_trackPageview', pathname]);
    }
  }, [pathname]);

  // 使用suppressHydrationWarning避免水合警告
  // 确保类名与服务器端渲染和useEffect中设置的一致
  return (
    <body className="font-sans antialiased bg-background text-foreground" suppressHydrationWarning>
      {children}
    </body>
  );
}
