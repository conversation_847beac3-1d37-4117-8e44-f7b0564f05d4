/**
 * API服务类，用于统一管理所有API请求
 */

// API基础URL
export const API_BASE_URL = 'https://api.planetclicker.pro';

// APP名称，用于数据库选择
export const APP_NAME = 'planetclicker_pro';

// 游戏分页默认参数
export const DEFAULT_PAGE_SIZE = 20;

/**
 * 构建带有appname参数的URL
 * @param endpoint API端点路径
 * @param params 额外的查询参数
 * @returns 完整的API URL
 */
export function buildApiUrl(endpoint: string, params: Record<string, string | number | boolean> = {}): string {
  // 确保endpoint不以/开头
  const path = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
  
  // 创建URL对象
  const url = new URL(`${API_BASE_URL}/${path}`);
  
  // 添加appname参数
  url.searchParams.append('appname', APP_NAME);
  
  // 添加其他参数
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.append(key, String(value));
  });
  
  return url.toString();
}

/**
 * 执行GET请求
 * @param endpoint API端点
 * @param params 查询参数
 * @param options fetch选项
 * @returns 响应数据
 */
export async function apiGet<T>(
  endpoint: string, 
  params: Record<string, string | number | boolean> = {},
  options: RequestInit = {}
): Promise<T> {
  const url = buildApiUrl(endpoint, params);
  console.log(`API GET请求: ${url}`);
  
  // 添加请求超时功能
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        ...options.headers,
      },
      signal: controller.signal,
      ...options,
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    return data as T;
  } catch (error: any) {
    // 检查是否是超时错误
    if (error.name === 'AbortError') {
      console.error(`API请求超时: ${url}`);
      throw new Error(`请求超时，请稍后再试`);
    }
    
    // 处理其他错误
    console.error(`API请求错误: ${url}`, error);
    throw error;
  }
}

/**
 * 执行POST请求
 * @param endpoint API端点
 * @param body 请求体
 * @param params 查询参数
 * @param options fetch选项
 * @returns 响应数据
 */
export async function apiPost<T>(
  endpoint: string,
  body: any,
  params: Record<string, string | number | boolean> = {},
  options: RequestInit = {}
): Promise<T> {
  const url = buildApiUrl(endpoint, params);
  console.log(`API POST请求: ${url}`);
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers,
    },
    body: JSON.stringify(body),
    ...options,
  });
  
  if (!response.ok) {
    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
  }
  
  const data = await response.json();
  return data as T;
}

// 常用接口响应类型定义
export interface ApiResponse<T> {
  status: string;
  data: T;
  total?: number;
}

// 游戏数据接口
export interface ApiGame {
  game_id: number;
  game_name: string; // slug
  name: string;      // title
  image: string;
  plays?: number;
  rating?: string;
  description?: string;
  featured?: string;
  category_name?: string;
  instructions?: string;
  file?: string;
  game_type?: string;
  w?: number;
  h?: number;
  date_added?: string;
  published?: string;
  mobile?: string;
  video_url?: string;
  tags?: {
    id: number;
    name: string;
    url: string;
  }[];
  like_count?: number;
  favorite_count?: number;
}

// 游戏API相关方法
export const gameApi = {
  /**
   * 获取精选游戏
   */
  getFeaturedGames: (limit: number = DEFAULT_PAGE_SIZE) => 
    apiGet<ApiResponse<ApiGame[]>>('games/featured', { limit }),
  
  /**
   * 获取最新游戏
   */
  getLatestGames: (limit: number = DEFAULT_PAGE_SIZE) => 
    apiGet<ApiResponse<ApiGame[]>>('games/latest', { limit }),
  
  /**
   * 获取分类游戏
   */
  getCategoryGames: (categoryName: string, page: number = 1, limit: number = DEFAULT_PAGE_SIZE) => 
    apiGet<ApiResponse<ApiGame[]>>(`games/category/name/${encodeURIComponent(categoryName)}`, { 
      page, 
      limit 
    }),
  
  /**
   * 获取标签游戏
   */
  getTagGames: (tagName: string, page: number = 1, limit: number = DEFAULT_PAGE_SIZE) => 
    apiGet<ApiResponse<ApiGame[]>>(`games/tag/name/${encodeURIComponent(tagName)}`, { 
      page, 
      limit 
    }),
  
  /**
   * 获取游戏详情
   */
  getGameDetail: (slug: string) => 
    apiGet<ApiResponse<ApiGame>>(`games/game/${slug}`, {}),
  
  /**
   * 搜索游戏
   */
  searchGames: (keyword: string, page: number = 1, limit: number = DEFAULT_PAGE_SIZE) => 
    apiGet<ApiResponse<ApiGame[]>>('games/search', { 
      keyword: encodeURIComponent(keyword.trim()), 
      page, 
      limit,
      offset: (page - 1) * limit
    }),
    
  /**
   * 获取相关游戏 - 基于游戏分类
   */
  getRelatedGamesByCategory: (categoryName: string, excludeGameId: number, limit: number = 10) => 
    apiGet<ApiResponse<ApiGame[]>>(`games/category/name/${encodeURIComponent(categoryName)}`, { limit }),
    
  /**
   * 获取相关游戏 - 基于游戏标签
   */
  getRelatedGamesByTag: (tagUrl: string, excludeGameId: number, limit: number = 10) => 
    apiGet<ApiResponse<ApiGame[]>>(`games/tag/name/${encodeURIComponent(tagUrl)}`, { limit }),
  
  /**
   * 获取分类列表
   */
  getCategories: () => 
    apiGet<ApiResponse<{id: number; name: string; slug: string; count: number}[]>>('categories/home', {}),
};

/**
 * 带重试功能的API请求
 * @param apiCall API调用函数
 * @param retries 重试次数
 * @param delay 重试延迟(毫秒)
 * @returns API响应
 */
export async function withRetry<T>(
  apiCall: () => Promise<T>, 
  retries: number = 2, 
  delay: number = 1000
): Promise<T> {
  try {
    return await apiCall();
  } catch (error) {
    if (retries <= 0) throw error;
    
    console.log(`API请求失败，将在${delay}ms后重试...`);
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return withRetry(apiCall, retries - 1, delay * 1.5);
  }
} 