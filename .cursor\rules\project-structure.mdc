---
description:
globs:
alwaysApply: false
---
# 项目结构

这是一个基于Next.js和Tailwind CSS的小游戏网站项目。

## 主要目录结构

- `src/app/`: 包含所有页面路由和布局组件
  - `app/page.tsx`: 网站主页
  - `app/layout.tsx`: 全局布局
  - `app/game/`: 游戏详情页
  - `app/category/`: 分类页面
  - `app/tag/`: 标签页面
  - `app/search/`: 搜索页面

- `src/components/`: 组件库
  - `components/layout/`: 布局相关组件
  - `components/game/`: 游戏相关组件
  - `components/ui/`: UI通用组件
  - `components/ads/`: 广告相关组件

- `src/lib/`: 工具函数和API客户端
- `src/config/`: 配置文件
- `src/styles/`: 样式文件
- `public/`: 静态资源

## 主要技术栈

- Next.js: React框架，用于构建服务端渲染和静态生成的网站
- Tailwind CSS: 用于样式设计
- TypeScript: 类型安全的JavaScript
