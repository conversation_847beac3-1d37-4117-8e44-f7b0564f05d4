import type { GameCardProps } from '@/components/game/GameCard';

export const featuredGames: GameCardProps[] = [
  {
    id: '1',
    title: 'Idle Blogger Simulator',
    image: 'https://imgs.crazygames.com/idle-blogger-simulator_16x9/20250407074611/idle-blogger-simulator_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'idle-blogger-simulator',
    badge: 'updated'
  },
  {
    id: '2',
    title: 'Checkers & Draughts Multiplayer',
    image: 'https://imgs.crazygames.com/checkers-draughts-multiplayer_16x9/20250327132036/checkers-draughts-multiplayer_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'checkers-draughts-multiplayer'
  },
  {
    id: '3',
    title: 'Traffic Rider',
    image: 'https://imgs.crazygames.com/traffic-rider-vvq_16x9/20250328101418/traffic-rider-vvq_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'traffic-rider'
  },
  {
    id: '4',
    title: 'Bridge Race',
    image: 'https://imgs.crazygames.com/bridge-race_16x9/20241227062023/bridge-race_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'bridge-race',
    badge: 'hot'
  },
  {
    id: '5',
    title: 'Murder Mafia',
    image: 'https://imgs.crazygames.com/murder-mafia_16x9/20230915113149/murder-mafia_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'murder-mafia',
    badge: 'hot'
  },
  {
    id: '6',
    title: 'Teacher Simulator',
    image: 'https://imgs.crazygames.com/teacher-simulator/20220907173822/teacher-simulator-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'teacher-simulator',
    badge: 'hot'
  },
  {
    id: '7',
    title: 'Super Bike The Champion',
    image: 'https://imgs.crazygames.com/super-bike-the-champion_16x9/20250408025955/super-bike-the-champion_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'super-bike-the-champion',
    badge: 'updated'
  },
  {
    id: '8',
    title: 'Stickman Clash',
    image: 'https://imgs.crazygames.com/stickman-clash_16x9/20250221023221/stickman-clash_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'stickman-clash'
  }
];

export const newGames: GameCardProps[] = [
  {
    id: '9',
    title: 'Tap Wood Block Away',
    image: 'https://imgs.crazygames.com/idle-blogger-simulator_16x9/20250407074611/idle-blogger-simulator_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'tap-wood-block-away',
    badge: 'new'
  },
  {
    id: '10',
    title: 'Mystery Forest Match',
    image: 'https://ext.same-assets.com/2995625192/144752613.jpeg',
    slug: 'mystery-forest-match',
    badge: 'new'
  },
  {
    id: '11',
    title: 'Block Tour',
    image: 'https://ext.same-assets.com/2995625192/4230656290.jpeg',
    slug: 'block-tour',
    badge: 'new'
  },
  {
    id: '12',
    title: 'Puzzle Survivor',
    image: 'https://ext.same-assets.com/2995625192/1062390756.jpeg',
    slug: 'puzzle-survivor',
    badge: 'new'
  },
  {
    id: '13',
    title: 'Raid Heroes: Dragon Age',
    image: 'https://ext.same-assets.com/2995625192/416703271.jpeg',
    slug: 'raid-heroes',
    badge: 'new'
  },
  {
    id: '14',
    title: 'Super Sonic Run',
    image: 'https://imgs.crazygames.com/super-sonic-run_16x9/20250407111613/super-sonic-run_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'super-sonic-run',
    badge: 'new'
  }
];

export const trendingGames: GameCardProps[] = [
  {
    id: '15',
    title: 'Bloxd.io',
    image: 'https://imgs.crazygames.com/games/bloxdhop-io/cover_16x9-1709115453824.png?metadata=none&quality=85&width=675&fit=crop',
    slug: 'bloxdhop-io',
    badge: 'top-rated'
  },
  {
    id: '16',
    title: 'Ragdoll Archers',
    image: 'https://imgs.crazygames.com/ragdoll-archers_16x9/20240205020743/ragdoll-archers_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'ragdoll-archers'
  },
  {
    id: '17',
    title: 'Count Masters: Stickman Games',
    image: 'https://imgs.crazygames.com/count-masters-stickman-games_16x9/20250220041115/count-masters-stickman-games_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'count-masters-stickman-games'
  },
  {
    id: '18',
    title: 'Slice Master',
    image: 'https://imgs.crazygames.com/slice-master_16x9/20240731033229/slice-master_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'slice-master'
  },
  {
    id: '19',
    title: 'Planet Smash Destruction',
    image: 'https://imgs.crazygames.com/solar-smash_16x9/20240722073047/solar-smash_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'solar-smash'
  }
];

export const originalGames: GameCardProps[] = [
  {
    id: '20',
    title: 'Space Waves',
    image: 'https://ext.same-assets.com/2995625192/4126705833.jpeg',
    slug: 'space-waves'
  },
  {
    id: '21',
    title: 'Sky Riders',
    image: 'https://imgs.crazygames.com/sky-riders/20230830112358/sky-riders-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'sky-riders'
  },
  {
    id: '22',
    title: 'Cubes 2048',
    image: 'https://imgs.crazygames.com/cubes-2048_16x9/20231109112050/cubes-2048_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: 'cubes-2048'
  },
  {
    id: '23',
    title: 'Escape from Prison',
    image: 'https://imgs.crazygames.com/games/the-prison-escape/cover-1689173409361.png?metadata=none&quality=85&width=273&fit=crop',
    slug: 'escape-from-prison'
  }
];

export const ioGames: GameCardProps[] = [
  {
    id: '24',
    title: '8 Ball Billiards Classic',
    image: 'https://imgs.crazygames.com/8-ball-billiards-classic_16x9/20231108025958/8-ball-billiards-classic_16x9-cover?metadata=none&quality=85&width=273&fit=crop',
    slug: '8-ball-billiards-classic'
  },
  {
    id: '25',
    title: 'Crazy Flips 3D',
    image: 'https://imgs.crazygames.com/games/crazy-flips-3d/cover_16x9-1717520212223.png?metadata=none&quality=85&width=273&fit=crop',
    slug: 'crazy-flips-3d'
  }
];
