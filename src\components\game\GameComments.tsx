"use client";

import { useState } from 'react';
import Image from 'next/image';
import { ThumbsUp, ThumbsDown, Flag, Send } from 'lucide-react';

interface Comment {
  id: string;
  username: string;
  avatar: string;
  date: string;
  content: string;
  likes: number;
  dislikes: number;
  userLiked: boolean | null; // null = not voted, true = liked, false = disliked
}

interface GameCommentsProps {
  gameTitle: string;
}

const GameComments = ({ gameTitle }: GameCommentsProps) => {
  const [commentText, setCommentText] = useState('');
  const [comments, setComments] = useState<Comment[]>([
    {
      id: '1',
      username: 'GameMaster42',
      avatar: 'https://i.pravatar.cc/150?img=1',
      date: '2 days ago',
      content: "I love this game! It's so addictive and the graphics are amazing. I've been playing for hours and can't stop. Definitely one of the best games on H5Play.",
      likes: 42,
      dislikes: 3,
      userLiked: null
    },
    {
      id: '2',
      username: 'ProGamer99',
      avatar: 'https://i.pravatar.cc/150?img=2',
      date: '1 week ago',
      content: "Really fun game but I found a bug in level 3. The checkpoint doesn't work properly.",
      likes: 18,
      dislikes: 2,
      userLiked: null
    },
    {
      id: '3',
      username: 'CasualPlayer',
      avatar: 'https://i.pravatar.cc/150?img=3',
      date: '2 weeks ago',
      content: 'Great game to play during breaks. Simple controls and fun gameplay!',
      likes: 27,
      dislikes: 0,
      userLiked: null
    }
  ]);

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!commentText.trim()) return;
    
    const newComment: Comment = {
      id: Math.random().toString(36).substr(2, 9),
      username: 'You',
      avatar: 'https://i.pravatar.cc/150?img=5',
      date: 'Just now',
      content: commentText,
      likes: 1,
      dislikes: 0,
      userLiked: true
    };
    
    setComments([newComment, ...comments]);
    setCommentText('');
  };

  const handleVote = (commentId: string, isLike: boolean) => {
    setComments(comments.map(comment => {
      if (comment.id === commentId) {
        const wasLiked = comment.userLiked === true;
        const wasDisliked = comment.userLiked === false;
        
        let newLikes = comment.likes;
        let newDislikes = comment.dislikes;
        let newUserLiked = comment.userLiked;
        
        if (isLike) {
          // Clicked like button
          if (wasLiked) {
            // Unlike
            newLikes--;
            newUserLiked = null;
          } else {
            // Like
            newLikes++;
            if (wasDisliked) {
              newDislikes--;
            }
            newUserLiked = true;
          }
        } else {
          // Clicked dislike button
          if (wasDisliked) {
            // Undislike
            newDislikes--;
            newUserLiked = null;
          } else {
            // Dislike
            newDislikes++;
            if (wasLiked) {
              newLikes--;
            }
            newUserLiked = false;
          }
        }
        
        return {
          ...comment,
          likes: newLikes,
          dislikes: newDislikes,
          userLiked: newUserLiked
        };
      }
      
      return comment;
    }));
  };

  return (
    <div className="bg-card rounded-lg overflow-hidden">
      <div className="p-4 border-b border-border">
        <h2 className="text-xl font-bold">Comments ({comments.length})</h2>
        
        <form onSubmit={handleCommentSubmit} className="mt-4">
          <div className="flex gap-3 mb-3">
            <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0">
              <Image
                src="https://i.pravatar.cc/150?img=5"
                alt="Your Avatar"
                width={40}
                height={40}
                className="object-cover"
              />
            </div>
            <div className="flex-1">
              <textarea
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                placeholder={`What do you think about ${gameTitle}?`}
                className="w-full p-3 bg-background border border-border rounded focus:outline-none focus:ring-1 focus:ring-primary resize-none"
                rows={3}
              ></textarea>
            </div>
          </div>
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={!commentText.trim()}
              className="bg-primary hover:bg-primary/90 text-white rounded px-4 py-2 flex items-center gap-2 disabled:opacity-50 disabled:pointer-events-none"
            >
              <Send size={16} />
              <span>Post Comment</span>
            </button>
          </div>
        </form>
      </div>
      
      <div className="p-4">
        {comments.map((comment) => (
          <div key={comment.id} className="py-4 border-b border-border last:border-0">
            <div className="flex gap-3">
              <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0">
                <Image
                  src={comment.avatar}
                  alt={`${comment.username}'s Avatar`}
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <div>
                    <span className="font-bold">{comment.username}</span>
                    <span className="text-xs text-muted-foreground ml-2">{comment.date}</span>
                  </div>
                  <button className="text-muted-foreground hover:text-red-400">
                    <Flag size={16} />
                  </button>
                </div>
                <p className="my-2 text-sm">{comment.content}</p>
                <div className="flex gap-4">
                  <button
                    onClick={() => handleVote(comment.id, true)}
                    className={`flex items-center gap-1 text-sm ${
                      comment.userLiked === true ? 'text-blue-400' : 'text-muted-foreground hover:text-white'
                    }`}
                  >
                    <ThumbsUp size={14} />
                    <span>{comment.likes}</span>
                  </button>
                  <button
                    onClick={() => handleVote(comment.id, false)}
                    className={`flex items-center gap-1 text-sm ${
                      comment.userLiked === false ? 'text-red-400' : 'text-muted-foreground hover:text-white'
                    }`}
                  >
                    <ThumbsDown size={14} />
                    <span>{comment.dislikes}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GameComments; 