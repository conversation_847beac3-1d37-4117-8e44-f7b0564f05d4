"use client";

import React from 'react';
import Image from 'next/image';
import PlatformFeatures from './PlatformFeatures';

// Popular tags - deleted, no longer used
// const popularTags = [
//   { id: 1, name: 'Action', url: 'action' },
//   { id: 2, name: 'Adventure', url: 'adventure' },
//   { id: 3, name: 'Puzzle', url: 'puzzle' },
//   { id: 4, name: 'Racing', url: 'racing' },
//   { id: 5, name: 'Shooting', url: 'shooting' },
//   { id: 6, name: 'Strategy', url: 'strategy' },
//   { id: 7, name: 'Multiplayer', url: 'multiplayer' },
//   { id: 8, name: 'Platform', url: 'platform' },
// ];

// Interface for API tag response


const WelcomeSection = () => {
  // Delete state and API loading logic, no longer needed
  // const [tags, setTags] = useState<ApiTag[]>([]);
  // const [isLoading, setIsLoading] = useState(true);

  // Delete useEffect with API call
  // useEffect(() => {
  //   const fetchTags = async () => {
  //     try {
  //       const apiBaseUrl = 'https://api.planetclicker.pro';
  //       const response = await fetch(`${apiBaseUrl}/tags/home`);
        
  //       if (!response.ok) {
  //         // If API request fails, use preset tags
  //         setTags(popularTags);
  //         return;
  //       }
        
  //       const result: ApiTagsResponse = await response.json();
        
  //       if (result.status === 'success' && Array.isArray(result.data)) {
  //         // Only get the first 8 tags
  //         setTags(result.data.slice(0, 8));
  //       } else {
  //         // If data format doesn't match, use preset tags
  //         setTags(popularTags);
  //       }
  //     } catch (error) {
  //       console.error('Error fetching tags:', error);
  //       // If there's an error, use preset tags
  //       setTags(popularTags);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   fetchTags();
  // }, []);

  return (
    <div className="py-4">
      <div className="flex items-center justify-center gap-3 mb-4">
        <div className="w-10 h-10 relative cosmic-glow">
          <Image
            src="https://ext.same-assets.com/2995625192/2563901416.svg"
            alt="Planet Clicker Logo"
            fill
            className="object-contain planet-orbit"
          />
        </div>
        <h1 className="text-2xl font-bold bg-gradient-to-r from-cosmic-purple via-cosmic-blue to-cosmic-nebula bg-clip-text text-transparent">
          Welcome to Planet Clicker
        </h1>
      </div>
      <div className="text-center mb-4">
        <p className="text-muted-foreground text-sm">
          🌌 Explore the universe of gaming • Click your way to the stars • Discover cosmic adventures
        </p>
      </div>
      
      {/* Tag navigation section removed */}
      
      <PlatformFeatures />
    </div>
  );
};

export default WelcomeSection; 