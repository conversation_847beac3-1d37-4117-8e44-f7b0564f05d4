---
description:
globs:
alwaysApply: false
---
# API端点

项目使用的API服务器基础URL: `https://api.planetclicker.pro`

## 游戏列表API

- 最新游戏: `GET /games/latest`
- 最热游戏: `GET /games/popular`
- 推荐游戏: `GET /games/featured`
- 评分最高: `GET /games/top-rated`

## 分类API

- 根据分类ID获取游戏: `GET /games/by-category/{category_id}`
- 根据分类名获取游戏: `GET /games/category/name/{category_name}`

## 标签API

- 根据标签ID获取游戏: `GET /games/tag/{tag_id}`
- 根据标签名获取游戏: `GET /games/tag/name/{tag_name}`

## 游戏详情API

- 通过游戏ID获取详情: `GET /games/{game_id}`
- 通过游戏名称获取详情: `GET /games/game/{game_name}`

## 搜索API

- 搜索游戏: `GET /games/search`
  - 参数: `keyword`, `category_id`, `tag_id`, `min_rating`, `max_rating`, `skip`, `limit`

## 互动API

- 切换游戏点赞状态: `POST /games/{game_id}/toggle-like`
- 切换游戏收藏状态: `POST /games/{game_id}/toggle-favorite`
