"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Home, 
  Clock,
  Gamepad2, 
  Flame,
  RefreshCw,
  Star,
  Users,
  User2,
  Swords,
  Mountain,
  CircleDot,
  Shirt,
  Bike,
  Car,
  Club,
  Coffee,
  Puzzle,
  Crosshair,
  Target,
  Trophy,
  Award,
  Dumbbell,
  Brain,
  Network,
  Castle,
  MousePointerClick,
  Joystick,
  Square,
  LayoutGrid,
  Music,
  Skull,
  PersonStanding,
  Sparkles,
  Activity,
  TowerControl,
  Shield,
  X, 
  ChevronRight,
  Loader2,
  Tags,
  Tag
} from 'lucide-react';
import { usePathname } from 'next/navigation';

// Interface for API Tag data
interface ApiTag {
  id: number;
  name: string;
  url: string; // Slug for the tag URL
}

// Interface for API response
interface TagsApiResponse {
  status: string;
  data: ApiTag[];
}

// Interface for a sidebar item
interface SidebarItem {
  icon: React.ReactNode;
  label: string;
  href: string;
}

// Helper function to map tag name/url to icon
const getTagIcon = (tagNameOrUrl: string): React.ReactNode => {
  const lowerCaseName = tagNameOrUrl.toLowerCase().replace(/\s+/g, '-'); // Normalize name for matching

  // More specific matches first
  if (lowerCaseName.includes('tower-defense')) return <TowerControl size={18} />;
  if (lowerCaseName.includes('stickman')) return <PersonStanding size={18} />;
  if (lowerCaseName.includes('clicker') || lowerCaseName.includes('idle')) return <MousePointerClick size={18} />;
  if (lowerCaseName.includes('driving')) return <Car size={18} />;
  if (lowerCaseName.includes('basketball')) return <CircleDot size={18} />;
  if (lowerCaseName.includes('board-game')) return <LayoutGrid size={18} />;
  if (lowerCaseName.includes('shooting')) return <Crosshair size={18} />;
  if (lowerCaseName.includes('soccer') || lowerCaseName.includes('football')) return <Trophy size={18} />;
  if (lowerCaseName.includes('zombie') || lowerCaseName.includes('horror')) return <Skull size={18} />;
  if (lowerCaseName.includes('music')) return <Music size={18} />;

  // General categories
  if (lowerCaseName.includes('action')) return <Swords size={18} />;
  if (lowerCaseName.includes('adventure')) return <Mountain size={18} />;
  if (lowerCaseName.includes('beauty') || lowerCaseName.includes('dress-up')) return <Sparkles size={18} />;
  if (lowerCaseName.includes('bike')) return <Bike size={18} />;
  if (lowerCaseName.includes('car') || lowerCaseName.includes('racing')) return <Car size={18} />;
  if (lowerCaseName.includes('card')) return <Club size={18} />;
  if (lowerCaseName.includes('casual')) return <Coffee size={18} />;
  if (lowerCaseName.includes('puzzle')) return <Puzzle size={18} />;
  if (lowerCaseName.includes('strategy')) return <Brain size={18} />;
  if (lowerCaseName.includes('multiplayer')) return <Users size={18} />;
  if (lowerCaseName.includes('2-player') || lowerCaseName.includes('two-player')) return <User2 size={18} />;
  if (lowerCaseName.includes('io') || lowerCaseName.includes('.io')) return <Gamepad2 size={18} />;
  if (lowerCaseName.includes('sports')) return <Trophy size={18} />;
  if (lowerCaseName.includes('arcade')) return <Joystick size={18} />;
  if (lowerCaseName.includes('platformer')) return <Square size={18} />;
  if (lowerCaseName.includes('skill')) return <Target size={18} />;
  if (lowerCaseName.includes('war')) return <Shield size={18} />;
  if (lowerCaseName.includes('simulation')) return <PersonStanding size={18} />;

   // Fallback icon if no specific match
  return <Star size={18} />; 
};

const Sidebar = () => {
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [tagItems, setTagItems] = useState<SidebarItem[]>([]);
  const [tagsLoading, setTagsLoading] = useState(true);
  const pathname = usePathname();

  // Close sidebar on path change on mobile
  useEffect(() => {
    setIsMobileOpen(false);
  }, [pathname]);

  // Close sidebar on wider screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMobileOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Listen for menu button clicks
  useEffect(() => {
    const handleMenuClick = () => {
      setIsMobileOpen(true);
    };

    const menuButton = document.querySelector('.nav-icon');
    menuButton?.addEventListener('click', handleMenuClick);

    return () => {
      menuButton?.removeEventListener('click', handleMenuClick);
    };
  }, []);

  // Trigger custom event when sidebar hover state changes
  const handleMouseEnter = () => {
    setIsHovered(true);
    window.dispatchEvent(new CustomEvent('sidebarHover', { detail: { hovered: true } }));
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    window.dispatchEvent(new CustomEvent('sidebarHover', { detail: { hovered: false } }));
  };

  // Fetch tags from API
  useEffect(() => {
    const fetchTags = async () => {
      setTagsLoading(true);
      try {
        const apiBaseUrl = 'https://api.planetclicker.pro';
        const response = await fetch(`${apiBaseUrl}/tags/home`); // Or /tags if /home doesn't exist
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result: TagsApiResponse = await response.json();

        if (result.status === 'success' && Array.isArray(result.data)) {
          const fetchedTags = result.data.map((tag): SidebarItem => ({
            label: tag.name,
            href: `/tag/${tag.url}`, // Assuming route structure /tag/[slug]
            icon: getTagIcon(tag.name) // Use the updated function
          }));
          setTagItems(fetchedTags);
        } else {
          console.error("Failed to fetch or parse tags:", result);
          setTagItems([]);
        }
      } catch (error) {
        console.error("Error fetching tags:", error);
        setTagItems([]);
      } finally {
        setTagsLoading(false);
      }
    };

    fetchTags();
  }, []); // Runs once on mount

  // Combine static and dynamic items
  const staticItemsStart: SidebarItem[] = [
    { icon: <Home size={18} />, label: 'Home', href: '/' },
    { icon: <Gamepad2 size={18} />, label: 'New', href: '/new' },
    { icon: <Flame size={18} />, label: 'Featured', href: '/featured' },
  ];

  const staticItemsEnd: SidebarItem[] = [
    { icon: <Tags size={18} />, label: 'All Tags', href: '/tags' },
  ];

  // 限制标签数量，并确保All Tags在最后
  const allSidebarItems = [...staticItemsStart, ...tagItems.slice(0, 10), ...staticItemsEnd];

  return (
    <>
      {/* Desktop Sidebar */}
      <aside 
        className={`bg-card hidden md:block fixed left-0 top-[64px] bottom-0 border-r border-border overflow-hidden transition-all duration-200 z-30 ${
          isHovered ? 'w-56' : 'w-14'
        }`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className="h-full">
          {/* Render combined list */} 
          {allSidebarItems.map((item) => (
            <Link 
              key={item.href} // Use href as key, assuming it's unique
              href={item.href} 
              className={`flex items-center px-3 py-2 hover:bg-background transition-colors ${
                pathname === item.href ? 'text-primary bg-background' : 'text-muted-foreground hover:text-white'
              }`}
              title={isHovered ? '' : item.label} // Show full title on hover when collapsed
            >
              <div className={`flex-shrink-0`}>
                {item.icon}
              </div>
              <span className={`ml-3 whitespace-nowrap overflow-hidden transition-all duration-200 ${
                isHovered ? 'opacity-100 max-w-32' : 'opacity-0 max-w-0 invisible'
              }`}>
                {item.label}
              </span>
            </Link>
          ))}
          
          {/* 删除底部的所有标签链接 */}
          
          {/* Optional: Show loader for tags section */} 
           {tagsLoading && isHovered && (
             <div className="flex items-center justify-center px-3 py-2">
               <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
             </div>
           )}
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <div 
        className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 md:hidden ${
          isMobileOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={() => setIsMobileOpen(false)}
      />
      
      <aside 
        className={`fixed top-0 left-0 h-full w-64 bg-card z-50 transition-transform duration-300 transform md:hidden overflow-hidden ${
          isMobileOpen ? 'translate-x-0' : '-translate-x-full'
        } flex flex-col py-4`}
      >
        <div className="flex items-center justify-between px-4 mb-4">
          <h2 className="font-bold text-lg">Menu</h2>
          <button 
            onClick={() => setIsMobileOpen(false)}
            className="p-1 rounded-lg hover:bg-background"
          >
            <X size={20} />
          </button>
        </div>

        <div className="flex-1 overflow-hidden">
           {/* Render combined list for mobile */} 
          {allSidebarItems.map((item) => (
            <Link 
              key={item.href} 
              href={item.href} 
              className={`flex items-center px-4 py-2 mb-0.5 hover:bg-background transition-colors ${
                pathname === item.href ? 'text-primary bg-background' : 'text-foreground'
              }`}
            >
              <div>
                {item.icon}
              </div>
              <span className={`ml-3`}>
                {item.label}
              </span>
              <ChevronRight size={16} className="ml-auto text-muted-foreground" />
            </Link>
          ))}
           
           {/* 删除移动端底部的所有标签链接 */}
           
           {/* Optional: Show loader for tags section */} 
           {tagsLoading && (
             <div className="flex items-center justify-center px-4 py-2">
               <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
             </div>
           )}
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
