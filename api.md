# Game API Server

使用 FastAPI 构建的游戏 API 服务器，提供游戏列表、分类、标签等信息。

## 功能特点

- 获取最新游戏列表
- 获取最受欢迎的游戏列表
- 获取推荐游戏列表
- 获取评分最高的游戏列表
- 获取所有游戏分类
- 获取首页显示的分类
- 根据分类获取游戏列表
- 根据标签获取游戏列表
- 获取游戏详情
- 游戏点赞和收藏功能

## API 接口

### 游戏相关接口

#### 游戏列表接口

1. 获取最新游戏列表
   - 路径: `GET /games/latest`
   - 返回: 最新添加的游戏列表
   - 响应示例:
   ```json
   {
     "status": "success",
     "data": [
       {
         "game_id": 123,
         "game_name": "super-jump",
         "name": "超级跳跃",
         "image": "game1.jpg",
         "plays": 1500,
         "rating": "4.5",
         "description": "一个有趣的跳跃游戏",
         "file": "game1.swf",
         "featured": "1",
         "category_name": "休闲游戏",
         "tags_name": "跳跃, 休闲, 冒险"
       },
       // 更多游戏...
     ],
     "total": 150
   }
   ```

2. 获取最热游戏列表
   - 路径: `GET /games/popular`
   - 返回: 按游戏点击量排序的游戏列表
   - 响应格式与获取最新游戏列表相同

3. 获取推荐游戏列表
   - 路径: `GET /games/featured`
   - 返回: 推荐的游戏列表
   - 响应格式与获取最新游戏列表相同

4. 获取评分最高的游戏列表
   - 路径: `GET /games/top-rated`
   - 返回: 按评分排序的游戏列表
   - 响应格式与获取最新游戏列表相同

5. 根据分类ID获取游戏列表
   - 路径: `GET /games/by-category/{category_id}`
   - 返回: 指定分类下的游戏列表
   - 别名: `GET /games/category/{category_id}` (两个路径都可以使用)
   - 响应格式与获取最新游戏列表相同

6. 根据分类名获取游戏列表
   - 路径: `GET /games/category/name/{category_name}`
   - 返回: 指定分类名称下的游戏列表
   - 响应格式与获取最新游戏列表相同

7. 根据标签ID获取游戏列表
   - 路径: `GET /games/tag/{tag_id}`
   - 返回: 含有指定标签的游戏列表
   - 响应格式与获取最新游戏列表相同

8. 根据标签名获取游戏列表
   - 路径: `GET /games/tag/name/{tag_name}`
   - 返回: 含有指定标签的游戏列表
   - 别名: `GET /games/by-tag/{tag_name}` (兼容旧请求)
   - 响应格式与获取最新游戏列表相同

#### 游戏搜索接口

- 路径: `GET /games/search`
- 描述: 搜索游戏，支持关键词搜索和多种过滤条件
- 参数:
  - `keyword`: 搜索关键词（必填）
  - `category_id`: 分类ID（可选）
  - `tag_id`: 标签ID（可选）
  - `min_rating`: 最低评分（可选）
  - `max_rating`: 最高评分（可选）
  - `skip`: 跳过的数量（默认值：0）
  - `limit`: 返回的数量（默认值：10）
- 搜索范围: 游戏名称、游戏描述
- 排序: 按添加时间倒序
- 响应示例:
```json
{
  "status": "success",
  "data": [
    {
      "game_id": 123,
      "game_name": "super-jump",
      "name": "超级跳跃",
      "image": "game1.jpg",
      "plays": 1500,
      "rating": "4.5",
      "description": "一个有趣的跳跃游戏",
      "file": "game1.swf",
      "featured": "1",
      "category_name": "休闲游戏",
      "tags_name": "跳跃, 休闲, 冒险"
    },
    // 更多匹配的游戏...
  ],
  "total": 25
}
```

#### 游戏详情接口

1. 通过游戏ID获取详情
   - 路径: `GET /games/{game_id}`
   - 返回: 指定游戏的详细信息

2. 通过游戏名称获取详情（SEO友好）
   - 路径: `GET /games/game/{game_name}`
   - 返回: 指定游戏的详细信息
   - 说明: 此接口更适合SEO，建议前端使用此路径

响应示例:
```json
{
  "status": "success",
  "data": {
    "game_id": 123,
    "catalog_id": "G-123",
    "game_name": "super-jump",
    "name": "超级跳跃",
    "image": "game1.jpg",
    "plays": 1500,
    "rating": "4.5",
    "description": "一个有趣的跳跃游戏，玩家需要控制角色跳过各种障碍物...",
    "instructions": "使用方向键控制角色移动，按空格键跳跃",
    "file": "game1.swf",
    "game_type": "html5",
    "w": 800,
    "h": 600,
    "date_added": "2023-08-15T14:30:00",
    "published": "1",
    "featured": "1",
    "mobile": "1",
    "video_url": "https://example.com/videos/game1.mp4",
    "category_name": "休闲游戏",
    "tags": [
      {"id": 1, "name": "跳跃", "url": "jump"},
      {"id": 2, "name": "休闲", "url": "casual"},
      {"id": 3, "name": "冒险", "url": "adventure"}
    ],
    "like_count": 156,
    "favorite_count": 89
  }
}
```

### 游戏互动接口

1. 切换游戏点赞状态
   - 路径: `POST /games/{game_id}/toggle-like`
   - 描述: 切换游戏的点赞状态（点赞/取消点赞）
   - 限制: 同一IP 24小时内只能对同一游戏点赞一次
   - 返回: 包含更新后的点赞数和当前点赞状态的响应
   - 响应示例:
   ```json
   {
     "status": "success",
     "message": "点赞成功", // 或 "取消点赞成功"
     "like_count": 156,
     "is_liked": true
   }
   ```

2. 切换游戏收藏状态
   - 路径: `POST /games/{game_id}/toggle-favorite`
   - 描述: 切换游戏的收藏状态（收藏/取消收藏）
   - 限制: 同一IP 24小时内只能对同一游戏收藏一次
   - 返回: 包含更新后的收藏数和当前收藏状态的响应
   - 响应示例:
   ```json
   {
     "status": "success",
     "message": "收藏成功", // 或 "取消收藏成功"
     "favorite_count": 89,
     "is_favorited": true
   }
   ```

### 获取随机游戏

- 路径: `GET /games/random`
- 描述: 随机获取一个游戏。支持从不同来源随机选择，并可以按分类和标签筛选。
- 查询参数：
  - source: 随机游戏来源
    - all: 所有游戏（默认）
    - latest: 最新游戏（从最新100个游戏中随机）
    - hot: 最热游戏（从最热门100个游戏中随机）
  - category_id: 分类ID（可选）
  - tag_id: 标签ID（可选）
- 响应示例:
```json
{
  "status": "success",
  "data": {
    "game_id": 123,
    "game_name": "super-jump",
    "name": "超级跳跃",
    "image": "game1.jpg",
    "plays": 1500,
    "rating": "4.5",
    "description": "一个有趣的跳跃游戏",
    "file": "game1.swf",
    "featured": "1",
    "category_name": "休闲游戏",
    "tags_name": "跳跃, 休闲, 冒险"
  }
}
```

### 页面描述接口

#### 获取页面描述

- 路径: `GET /pages/{page_url}`
- 描述: 获取指定页面的描述文本信息
- 参数：
  - page_url: 页面URL，例如 "/" 表示首页，"/about" 表示关于页面
- 响应示例：
```json
{
  "status": "success",
  "data": {
    "page_name": "home",
    "page_url": "/",
    "description": "欢迎来到游戏网站！这里有最新最好玩的游戏。",
    "has_content": "1",
    "content_value": "更多详细内容..."
  }
}
```

### 分类相关接口

#### 获取分类列表

- 路径: `GET /categories`
- 描述: 获取所有游戏分类
- 参数：
  - `skip`: 跳过的数量（默认值：0）
  - `limit`: 返回的数量（默认值：50）
- 响应示例:
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "休闲游戏",
      "image": "casual.jpg",
      "show_home": 1
    },
    {
      "id": 2,
      "name": "动作游戏",
      "image": "action.jpg",
      "show_home": 1
    },
    {
      "id": 3,
      "name": "冒险游戏",
      "image": "adventure.jpg",
      "show_home": 0
    }
    // 更多分类...
  ]
}
```

#### 获取首页显示的分类

- 路径: `GET /categories/home`
- 描述: 获取设置为在首页显示的分类
- 响应格式与获取所有分类列表相同，但只返回 `show_home` 值为 1 的分类

#### 获取特定分类详情

- 路径: `GET /categories/{category_id}`
- 描述: 获取指定ID的分类详情
- 响应示例:
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "休闲游戏",
      "image": "casual.jpg",
      "show_home": 1
    }
  ]
}
```

### 标签相关接口

#### 获取标签列表

- 路径: `GET /tags` 或 `GET /tags/home`
- 描述: 获取所有标签列表
- 响应示例:
```json
{
  "status": "success",
  "data": [
    {"id": 1, "name": "跳跃", "url": "jump"},
    {"id": 2, "name": "休闲", "url": "casual"},
    {"id": 3, "name": "冒险", "url": "adventure"},
    {"id": 4, "name": "动作", "url": "action"}
    // 更多标签...
  ]
}
```

## 查询参数

所有列表接口都支持以下查询参数用于分页：

方式一：使用 `skip` 和 `limit`
- `skip`: 跳过的数量，默认为 0
- `limit`: 返回的数量，默认为 10

方式二：使用 `page` 和 `limit`
- `page`: 页码，从 1 开始
- `limit`: 每页数量，默认为 10

两种分页方式等价，例如 `page=2&limit=10` 与 `skip=10&limit=10` 效果相同。

## 响应格式

所有 API 响应都遵循以下格式：

```json
{
  "status": "success",
  "data": [...],  // 数组或对象，根据接口不同
  "total": 100    // 总数，仅列表接口返回
}
```

## 错误处理

当请求出错时，API 会返回相应的错误状态码和详细信息。常见错误包括：

- 404 Not Found: 请求的资源不存在
  ```json
  {
    "detail": "游戏不存在"
  }
  ```

- 500 Internal Server Error: 服务器内部错误
  ```json
  {
    "detail": "查询失败: [错误详情]"
  }
  ```

## 开发环境设置

1. 克隆项目
2. 安装依赖：`pip install -r requirements.txt`
3. 配置数据库连接（在 `.env` 文件中）
4. 运行项目：`python run.py`

## 技术栈

- FastAPI
- SQLAlchemy
- MySQL
- Pydantic

### 获取随机游戏

```
GET /games/random
```

随机获取一个游戏。支持从不同来源随机选择，并可以按分类和标签筛选。

查询参数：
- source: 随机游戏来源
  - all: 所有游戏（默认）
  - latest: 最新游戏（从最新100个游戏中随机）
  - hot: 最热游戏（从最热门100个游戏中随机）
- category_id: 分类ID（可选）
- tag_id: 标签ID（可选）

响应示例：
```json
{
    "status": "success",
    "data": {
        "game_id": 123,
        "game_name": "super-jump",
        "name": "超级跳跃",
        "image": "game1.jpg",
        "plays": 1500,
        "rating": "4.5",
        "description": "一个有趣的跳跃游戏",
        "file": "game1.swf",
        "featured": "1",
        "category_name": "休闲游戏",
        "tags_name": "跳跃, 休闲, 冒险"
    }
}
```