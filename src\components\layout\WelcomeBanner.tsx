"use client";

import React from 'react';
import Image from 'next/image';
import FeatureIcon from './FeatureIcon';

const WelcomeBanner = () => {
  const features = [
    {
      iconType: 'games' as const,
      text: '4000+ games',
    },
    {
      iconType: 'install' as const,
      text: 'No install needed',
    },
    {
      iconType: 'device' as const,
      text: 'On any device',
    },
    {
      iconType: 'friends' as const,
      text: 'Play with friends',
    },
    {
      iconType: 'free' as const,
      text: 'All for free',
    },
  ];

  return (
    <div className="flex items-center gap-4 py-4 my-2 overflow-x-auto">
      {/* <div className="flex items-center gap-2 min-w-fit">
        <Image
          src="https://ext.same-assets.com/2995625192/1743763944.svg"
          alt="Planet Clicker logo"
          width={36}
          height={36}
          className="h-8 w-8"
        />
        <h2 className="text-white font-semibold">Welcome to Planet Clicker</h2>
      </div> */}

      {features.map((feature) => (
        <div key={feature.text} className="flex items-center gap-2 min-w-fit">
          <FeatureIcon type={feature.iconType} size={20} />
          <span className="text-sm text-muted-foreground">{feature.text}</span>
        </div>
      ))}
    </div>
  );
};

export default WelcomeBanner;
