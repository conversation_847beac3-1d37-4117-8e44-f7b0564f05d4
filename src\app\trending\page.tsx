import Layout from '@/components/layout/Layout';
import GameCard from '@/components/game/GameCard';
import Link from 'next/link';
import type { GameCardProps } from '@/components/game/GameCard';

// API响应接口
interface ApiResponse {
  status: string;
  data: ApiGame[];
  total?: number;
}

// API游戏数据结构
interface ApiGame {
  game_id: number;
  game_name: string; // slug
  name: string;      // title
  image: string;
  plays?: number;
  rating?: string;
}

// 每页显示的游戏数量
const GAMES_PER_PAGE = 20;

// 获取热门游戏
async function fetchTrendingGames(page: number = 1): Promise<{ games: GameCardProps[]; total: number }> {
  try {
    const apiBaseUrl = 'https://api.planetclicker.pro';
    const endpoint = `${apiBaseUrl}/games/trending?page=${page}&limit=${GAMES_PER_PAGE}`;
    
    console.log(`Fetching trending games (page ${page})`);
    console.log(`API endpoint: ${endpoint}`);
    
    const response = await fetch(endpoint);
    
    if (!response.ok) {
      console.error(`Failed to fetch trending games: ${response.status}`);
      return { games: [], total: 0 };
    }
    
    const data: ApiResponse = await response.json();
    
    if (data.status === 'success' && Array.isArray(data.data)) {
      // 将API数据映射为GameCardProps
      const games = data.data.map((game, index): GameCardProps => ({
        id: game.game_id.toString(),
        slug: game.game_name,
        title: game.name,
        image: game.image,
        // 只给前几个游戏添加"hot"标签
        badge: index < 5 ? 'hot' : undefined
      }));
      
      console.log(`Successfully fetched ${games.length} trending games`);
      return { games, total: data.total || games.length };
    }
    
    console.error('Failed to parse game data from API response');
    return { games: [], total: 0 };
  } catch (error) {
    console.error('Error fetching trending games:', error);
    return { games: [], total: 0 };
  }
}

// 分页组件
function Pagination({ 
  currentPage, 
  totalPages 
}: { 
  currentPage: number; 
  totalPages: number;
}) {
  return (
    <div className="flex justify-center mt-8 mb-4">
      <div className="flex flex-wrap justify-center gap-1">
        {currentPage > 1 && (
          <Link
            href={`/trending?page=${currentPage - 1}`}
            className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
          >
            Prev
          </Link>
        )}
        
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          // Display page numbers around current page
          let pageNum = 1;
          if (totalPages <= 5) {
            // If total pages are less than 5, show all page numbers
            pageNum = i + 1;
          } else if (currentPage <= 3) {
            // If current page is in first 3 pages, show pages 1-5
            pageNum = i + 1;
          } else if (currentPage >= totalPages - 2) {
            // If current page is in last 3 pages, show last 5 pages
            pageNum = totalPages - 4 + i;
          } else {
            // Otherwise show 2 pages before and after current page
            pageNum = currentPage - 2 + i;
          }
          
          // Hide some page numbers on smallest screens
          const hideOnMobile = (i === 0 || i === 4) && totalPages > 3;
          
          return (
            <Link
              key={pageNum}
              href={`/trending?page=${pageNum}`}
              className={`${hideOnMobile ? 'hidden sm:block' : ''} px-2 sm:px-4 py-2 text-sm sm:text-base rounded ${
                currentPage === pageNum
                  ? 'bg-primary text-white'
                  : 'bg-card hover:bg-card/80 text-muted-foreground hover:text-white'
              }`}
            >
              {pageNum}
            </Link>
          );
        })}
        
        {currentPage < totalPages && (
          <Link
            href={`/trending?page=${currentPage + 1}`}
            className="px-2 sm:px-4 py-2 text-sm sm:text-base rounded bg-secondary hover:bg-secondary/80 text-white"
          >
            Next
          </Link>
        )}
      </div>
    </div>
  );
}

export default async function TrendingGamesPage({ searchParams }: { searchParams: { page?: string } }) {
  // Ensure searchParams are resolved
  const resolvedParams = await Promise.resolve(searchParams);
  const pageParam = resolvedParams.page || '1';
  const currentPage = parseInt(pageParam, 10);
  
  // Get trending games list
  const { games, total } = await fetchTrendingGames(currentPage);
  
  // Calculate total pages
  const totalPages = Math.ceil(total / GAMES_PER_PAGE);
  
  return (
    <Layout>
      <div className="max-w-screen-xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold mb-4">Trending Games</h1>
          <p className="text-muted-foreground">
            The most popular games right now. Play what everyone is playing!
          </p>
        </div>
        
        {games.length > 0 ? (
          <>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {games.map((game) => (
                <GameCard key={game.id} {...game} />
              ))}
            </div>
            
            {totalPages > 1 && (
              <Pagination 
                currentPage={currentPage} 
                totalPages={totalPages}
              />
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-muted-foreground">No trending games found</h3>
            <p className="mt-2">Check back later for hot games!</p>
          </div>
        )}
      </div>
    </Layout>
  );
} 