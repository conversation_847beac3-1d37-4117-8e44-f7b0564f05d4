"use client";

import { type ReactNode, useState, useEffect } from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import Footer from './Footer';
import ControllerBanner from './ControllerBanner';

interface LayoutProps {
  children: ReactNode;
  showControllerBanner?: boolean;
}

const Layout = ({
  children,
  showControllerBanner = true
}: LayoutProps) => {
  const [showBanner, setShowBanner] = useState(showControllerBanner);
  const [sidebarHovered, setSidebarHovered] = useState(false);

  // Listen for sidebar hover events from a custom event
  useEffect(() => {
    const handleSidebarHover = (e: CustomEvent) => {
      setSidebarHovered(e.detail.hovered);
    };

    window.addEventListener('sidebarHover' as any, handleSidebarHover);
    return () => {
      window.removeEventListener('sidebarHover' as any, handleSidebarHover);
    };
  }, []);

  return (
    <div className="flex min-h-screen flex-col">
      {/* 独立的星星组 */}
      <div className="star-group-1"></div>
      <div className="star-group-2"></div>
      <div className="star-group-3"></div>
      <div className="star-group-4"></div>

      <Header />
      <div className="flex flex-1">
        <Sidebar />
        <main className="flex-1 md:ml-14">
          <div className="container mx-auto px-4 py-6 max-w-screen-2xl">
            {/* 移除了ControllerBanner组件 */}
            {children}
          </div>
        </main>
      </div>
      <Footer />
    </div>
  );
};

export default Layout;
