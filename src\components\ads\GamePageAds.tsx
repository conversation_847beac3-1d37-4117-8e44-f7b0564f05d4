import React from 'react';
import SidebarAd from './SidebarAd';

export interface GamePageAdsProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * 游戏页面广告布局组件，在游戏内容两侧添加广告位
 */
export default function GamePageAds({
  children,
  className = ''
}: GamePageAdsProps) {
  return (
    <div className={`w-full max-w-screen-xl mx-auto flex justify-center items-start mt-8 ${className}`}>
      {/* 左侧广告 */}
      <SidebarAd position="left" className="mt-0" />
      
      {/* 中间游戏内容 */}
      <div className="max-w-4xl w-full px-4">
        {children}
      </div>
      
      {/* 右侧广告 */}
      <SidebarAd position="right" className="mt-0" />
    </div>
  );
} 