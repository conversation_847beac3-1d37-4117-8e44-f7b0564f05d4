import React from 'react';
import AdPlaceholder from './AdPlaceholder';

export interface SidebarAdProps {
  position?: 'left' | 'right';
  className?: string;
}

/**
 * 侧边栏广告组件，用于游戏页面两侧
 */
export default function SidebarAd({
  position = 'left',
  className = ''
}: SidebarAdProps) {
  return (
    <div className={`hidden lg:block ${position === 'left' ? 'mr-4' : 'ml-4'} ${className}`}>
      <AdPlaceholder
        type="sidebar"
        width="160px"
        height="600px"
      />
    </div>
  );
} 