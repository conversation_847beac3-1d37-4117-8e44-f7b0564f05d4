import Layout from '@/components/layout/Layout';
import WelcomeSection from '@/components/layout/WelcomeSection';
import GameSection from '@/components/game/GameSection';
import type { GameCardProps } from '@/components/game/GameCard';
import { gameApi, ApiGame, ApiResponse } from '@/lib/api';

// Helper function to从API游戏数据转换为GameCardProps格式
function mapToGameCardProps(apiGame: ApiGame): GameCardProps {
  return {
    id: apiGame.game_id.toString(),
    title: apiGame.name,
    slug: apiGame.game_name,
    image: apiGame.image,
    // 可以添加更多属性
  };
}

// 获取游戏数据并转换为合适的格式
async function fetchAndMapGames(fetchFunction: () => Promise<ApiResponse<ApiGame[]>>): Promise<GameCardProps[]> {
  try {
    const response = await fetchFunction();
    
    if (response.status === 'success' && Array.isArray(response.data)) {
      return response.data.map(mapToGameCardProps);
    } 
    
    console.error(`获取游戏数据失败: ${response.status}`);
    return []; 
  } catch (error) {
    console.error('获取游戏数据时发生错误:', error);
    return [];
  }
}

// Make the component async to use await for fetching
export default async function Home() {
  console.log("Fetching data for Home page...");
  
  // 使用新的API服务获取七种不同类型的游戏
  const [
    featuredGamesData, 
    newGamesData, 
    actionGamesData, 
    puzzleGamesData,
    shootingGamesData,
    arcadeGamesData,
    multiplayerGamesData
  ] = await Promise.all([
    fetchAndMapGames(() => gameApi.getFeaturedGames(10)),
    fetchAndMapGames(() => gameApi.getLatestGames(10)), 
    fetchAndMapGames(() => gameApi.getCategoryGames('action games', 1, 10)),
    fetchAndMapGames(() => gameApi.getCategoryGames('puzzle games', 1, 10)),
    fetchAndMapGames(() => gameApi.getCategoryGames('shooting games', 1, 10)),
    fetchAndMapGames(() => gameApi.getCategoryGames('arcade games', 1, 10)),
    fetchAndMapGames(() => gameApi.getCategoryGames('multiplayer games', 1, 10)),
  ]);

  // Log fetched data lengths
  console.log(`Featured games fetched: ${featuredGamesData.length}`);
  console.log(`New games fetched: ${newGamesData.length}`);
  console.log(`Action games fetched: ${actionGamesData.length}`);
  console.log(`Puzzle games fetched: ${puzzleGamesData.length}`);
  console.log(`Shooting games fetched: ${shootingGamesData.length}`);
  console.log(`Arcade games fetched: ${arcadeGamesData.length}`);
  console.log(`Multiplayer games fetched: ${multiplayerGamesData.length}`);

  // Limit NEW badges to a maximum of 3
  // Only add the 'new' badge to the first 3 games, not all of them
  const MAX_NEW_BADGES = 3;
  const newGamesWithBadge = newGamesData.map((game, index) => ({
    ...game, 
    badge: index < MAX_NEW_BADGES ? 'new' as const : undefined
  }));
  
  console.log(`Games with NEW badge: ${newGamesWithBadge.filter(game => game.badge === 'new').length}`);

  return (
    <Layout>
      <WelcomeSection />

      <div className="space-y-12">
        <GameSection
          title="Featured games"
          viewMoreLink="/featured"
          games={featuredGamesData}
        />

        <GameSection
          title="New games"
          viewMoreLink="/new"
          games={newGamesWithBadge}
        />

        <GameSection
          title="Action Games"
          viewMoreLink="/category/action-games"
          games={actionGamesData}
        />

        <GameSection
          title="Puzzle Games"
          viewMoreLink="/category/puzzle-games" 
          games={puzzleGamesData}
        />

        <GameSection
          title="Shooting Games"
          viewMoreLink="/category/shooting-games"
          games={shootingGamesData}
        />

        <GameSection
          title="Arcade Games"
          viewMoreLink="/category/arcade-games"
          games={arcadeGamesData}
        />

        <GameSection
          title="Multiplayer Games"
          viewMoreLink="/category/multiplayer-games"
          games={multiplayerGamesData}
        />
      </div>
    </Layout>
  );
}
